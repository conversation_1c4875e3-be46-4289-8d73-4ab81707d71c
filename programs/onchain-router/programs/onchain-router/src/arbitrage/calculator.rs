//! 套利利润计算器
//! 
//! 负责精确计算套利利润、费用扣除和利润分配

use anchor_lang::prelude::*;
use crate::routing::types::{Route, RouteResult};
use crate::flash_loan::traits::FlashLoanProvider;
use crate::error::RouteError;
use super::{ArbitrageResult, ArbitrageConfig};

/// 费用明细
#[derive(Debug, Clone)]
pub struct FeeBreakdown {
    /// 闪电贷费用
    pub flash_loan_fee: u64,
    /// DEX交易费用总计
    pub dex_fees: u64,
    /// Gas费用估算
    pub gas_fee: u64,
    /// 协议费用
    pub protocol_fee: u64,
    /// 滑点损失
    pub slippage_loss: u64,
    /// 总费用
    pub total_fees: u64,
}

/// 利润分配明细
#[derive(Debug, Clone)]
pub struct ProfitDistribution {
    /// 总收益
    pub gross_profit: u64,
    /// 总费用
    pub total_costs: u64,
    /// 净利润
    pub net_profit: u64,
    /// 用户分得利润
    pub user_profit: u64,
    /// 协议分得利润
    pub protocol_profit: u64,
    /// 运营方分得利润
    pub operator_profit: u64,
}

/// 利润计算器
pub struct ArbitrageCalculator {
    /// 配置
    config: ArbitrageConfig,
    /// Gas价格（lamports per compute unit）
    gas_price: u64,
    /// 协议费率（基点）
    protocol_fee_bps: u16,
    /// 运营费率（基点）
    operator_fee_bps: u16,
}

impl ArbitrageCalculator {
    /// 创建新的利润计算器
    pub fn new(config: ArbitrageConfig) -> Self {
        Self {
            config,
            gas_price: 100, // 默认100 lamports per CU
            protocol_fee_bps: 30, // 0.3%
            operator_fee_bps: 20, // 0.2%
        }
    }
    
    /// 计算预估利润（执行前）
    pub fn estimate_profit(
        &self,
        routes: &[Route],
        flash_loan_amount: u64,
        flash_loan_provider: &dyn FlashLoanProvider,
    ) -> Result<ProfitEstimate> {
        msg!("开始计算预估利润 - 闪电贷金额: {}", flash_loan_amount);
        
        // 1. 估算交易输出
        let estimated_output = self.estimate_route_output(routes, flash_loan_amount)?;
        
        // 2. 计算费用明细
        let fee_breakdown = self.calculate_estimated_fees(
            routes,
            flash_loan_amount,
            flash_loan_provider,
        )?;
        
        // 3. 计算净利润
        let gross_profit = estimated_output.saturating_sub(flash_loan_amount);
        let net_profit = gross_profit.saturating_sub(fee_breakdown.total_fees);
        
        // 4. 计算成功概率权重
        let risk_adjusted_profit = self.apply_risk_adjustment(net_profit, routes)?;
        
        let estimate = ProfitEstimate {
            gross_profit,
            fee_breakdown,
            net_profit,
            risk_adjusted_profit,
            success_probability: self.estimate_success_probability(routes)?,
            estimated_output,
        };
        
        msg!("利润预估完成 - 毛利润: {}, 净利润: {}, 风险调整后: {}", 
            gross_profit, net_profit, risk_adjusted_profit);
        
        Ok(estimate)
    }
    
    /// 计算实际利润（执行后）
    pub fn calculate_actual_profit(
        &self,
        route_result: &RouteResult,
        flash_loan_amount: u64,
        flash_loan_fee: u64,
        execution_time_ms: u64,
    ) -> Result<ArbitrageResult> {
        msg!("开始计算实际利润 - 输出金额: {}, 闪电贷费用: {}", 
            route_result.amount_out, flash_loan_fee);
        
        // 1. 计算费用明细
        let fee_breakdown = self.calculate_actual_fees(
            route_result,
            flash_loan_fee,
            execution_time_ms,
        )?;
        
        // 2. 计算毛利润
        let gross_profit = route_result.amount_out.saturating_sub(flash_loan_amount);
        
        // 3. 计算净利润
        let net_profit = gross_profit.saturating_sub(fee_breakdown.total_fees);
        
        // 4. 验证利润合理性
        self.validate_profit_calculation(gross_profit, &fee_breakdown)?;
        
        let result = ArbitrageResult {
            route_result: route_result.clone(),
            flash_loan_fee,
            protocol_fee: fee_breakdown.protocol_fee,
            net_profit,
            risk_assessment: super::risk_manager::RiskAssessment::default(),
            execution_time_ms,
        };
        
        msg!("实际利润计算完成 - 毛利润: {}, 净利润: {}", gross_profit, net_profit);
        
        Ok(result)
    }
    
    /// 计算利润分配
    pub fn calculate_profit_distribution(
        &self,
        net_profit: u64,
        user_stake: u64,
        total_flash_loan: u64,
    ) -> Result<ProfitDistribution> {
        if net_profit == 0 {
            return Ok(ProfitDistribution {
                gross_profit: 0,
                total_costs: 0,
                net_profit: 0,
                user_profit: 0,
                protocol_profit: 0,
                operator_profit: 0,
            });
        }
        
        // 1. 计算协议费用
        let protocol_profit = net_profit
            .checked_mul(self.protocol_fee_bps as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::DivisionByZero)?;
        
        // 2. 计算运营费用
        let operator_profit = net_profit
            .checked_mul(self.operator_fee_bps as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::DivisionByZero)?;
        
        // 3. 计算用户利润
        let user_profit = net_profit
            .saturating_sub(protocol_profit)
            .saturating_sub(operator_profit);
        
        // 4. 根据用户投入比例调整（如果有自有资金）
        let adjusted_user_profit = if user_stake > 0 && total_flash_loan > 0 {
            let user_ratio = user_stake as f64 / (user_stake + total_flash_loan) as f64;
            let base_user_profit = (user_profit as f64 * 0.8) as u64; // 基础80%
            let stake_bonus = (user_profit as f64 * 0.2 * user_ratio) as u64; // 投入奖励20%
            base_user_profit + stake_bonus
        } else {
            user_profit
        };
        
        let distribution = ProfitDistribution {
            gross_profit: net_profit + protocol_profit + operator_profit,
            total_costs: protocol_profit + operator_profit,
            net_profit,
            user_profit: adjusted_user_profit,
            protocol_profit,
            operator_profit,
        };
        
        msg!("利润分配计算完成 - 用户: {}, 协议: {}, 运营: {}", 
            adjusted_user_profit, protocol_profit, operator_profit);
        
        Ok(distribution)
    }
    
    /// 估算路由输出
    fn estimate_route_output(&self, routes: &[Route], amount_in: u64) -> Result<u64> {
        let mut current_amount = amount_in;
        
        for route in routes {
            // 获取DEX费率
            let fee_bps = self.get_dex_fee_bps(&route.dex);
            
            // 扣除交易费用
            let fee = current_amount
                .checked_mul(fee_bps as u64)
                .ok_or(RouteError::MathOverflow)?
                .checked_div(10000)
                .ok_or(RouteError::DivisionByZero)?;
            
            let amount_after_fee = current_amount.saturating_sub(fee);
            
            // 应用汇率（简化计算，实际应查询AMM价格）
            let exchange_rate = self.get_estimated_exchange_rate(&route.input_mint, &route.output_mint)?;
            current_amount = (amount_after_fee as f64 * exchange_rate) as u64;
            
            // 应用滑点
            let slippage = self.estimate_slippage(amount_after_fee, &route.dex)?;
            current_amount = current_amount.saturating_sub(slippage);
        }
        
        Ok(current_amount)
    }
    
    /// 计算预估费用
    fn calculate_estimated_fees(
        &self,
        routes: &[Route],
        flash_loan_amount: u64,
        flash_loan_provider: &dyn FlashLoanProvider,
    ) -> Result<FeeBreakdown> {
        // 1. 闪电贷费用
        let flash_loan_fee = flash_loan_amount
            .checked_mul(flash_loan_provider.get_fee_bps() as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::DivisionByZero)?;
        
        // 2. DEX交易费用
        let dex_fees = self.calculate_dex_fees(routes, flash_loan_amount)?;
        
        // 3. Gas费用
        let gas_fee = self.estimate_gas_cost(routes)?;
        
        // 4. 协议费用（基于预估利润）
        let estimated_gross = self.estimate_route_output(routes, flash_loan_amount)?
            .saturating_sub(flash_loan_amount);
        let protocol_fee = estimated_gross
            .checked_mul(self.protocol_fee_bps as u64)
            .unwrap_or(0)
            .checked_div(10000)
            .unwrap_or(0);
        
        // 5. 滑点损失
        let slippage_loss = self.estimate_total_slippage(routes, flash_loan_amount)?;
        
        let total_fees = flash_loan_fee + dex_fees + gas_fee + protocol_fee + slippage_loss;
        
        Ok(FeeBreakdown {
            flash_loan_fee,
            dex_fees,
            gas_fee,
            protocol_fee,
            slippage_loss,
            total_fees,
        })
    }
    
    /// 计算实际费用
    fn calculate_actual_fees(
        &self,
        route_result: &RouteResult,
        flash_loan_fee: u64,
        execution_time_ms: u64,
    ) -> Result<FeeBreakdown> {
        // 1. 实际Gas费用
        let gas_fee = route_result.gas_used
            .checked_mul(self.gas_price)
            .ok_or(RouteError::MathOverflow)?;
        
        // 2. DEX费用（根据交易量计算）
        let dex_fees = self.calculate_actual_dex_fees(route_result.amount_in, route_result.amount_out)?;
        
        // 3. 协议费用
        let gross_profit = route_result.amount_out.saturating_sub(route_result.amount_in);
        let protocol_fee = gross_profit
            .checked_mul(self.protocol_fee_bps as u64)
            .unwrap_or(0)
            .checked_div(10000)
            .unwrap_or(0);
        
        // 4. 实际滑点损失
        let expected_amount = route_result.expected_amount.unwrap_or(route_result.amount_out);
        let slippage_loss = expected_amount.saturating_sub(route_result.amount_out);
        
        let total_fees = flash_loan_fee + dex_fees + gas_fee + protocol_fee + slippage_loss;
        
        Ok(FeeBreakdown {
            flash_loan_fee,
            dex_fees,
            gas_fee,
            protocol_fee,
            slippage_loss,
            total_fees,
        })
    }
    
    /// 获取DEX费率
    fn get_dex_fee_bps(&self, dex: &crate::routing::types::Dex) -> u16 {
        match dex {
            crate::routing::types::Dex::RaydiumClmm => 25,
            crate::routing::types::Dex::RaydiumCpmm => 30,
            crate::routing::types::Dex::Orca => 30,
            crate::routing::types::Dex::MeteoraLb => 15,
            crate::routing::types::Dex::MeteoraAmm => 20,
            crate::routing::types::Dex::PumpSwap => 100,
        }
    }
    
    /// 估算汇率
    fn get_estimated_exchange_rate(&self, input_mint: &Pubkey, output_mint: &Pubkey) -> Result<f64> {
        if input_mint == output_mint {
            return Ok(1.0);
        }
        
        // 简化的汇率估算（实际应查询链上价格）
        // 这里返回一个略低于1的汇率来模拟价格差异
        Ok(0.995 + (rand::random::<f64>() * 0.01)) // 0.995-1.005
    }
    
    /// 估算滑点
    fn estimate_slippage(&self, amount: u64, dex: &crate::routing::types::Dex) -> Result<u64> {
        // 根据DEX类型和交易金额估算滑点
        let base_slippage_bps = match dex {
            crate::routing::types::Dex::RaydiumClmm => 10,
            crate::routing::types::Dex::RaydiumCpmm => 15,
            crate::routing::types::Dex::Orca => 20,
            crate::routing::types::Dex::MeteoraLb => 8,
            crate::routing::types::Dex::MeteoraAmm => 10,
            crate::routing::types::Dex::PumpSwap => 50,
        };
        
        // 大额交易增加滑点
        let amount_factor = if amount > 100_000_000 { 1.5 } else { 1.0 }; // 0.1 SOL以上增加滑点
        
        let slippage = amount
            .checked_mul((base_slippage_bps as f64 * amount_factor) as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::DivisionByZero)?;
        
        Ok(slippage)
    }
    
    /// 计算DEX费用
    fn calculate_dex_fees(&self, routes: &[Route], initial_amount: u64) -> Result<u64> {
        let mut total_fees = 0u64;
        let mut current_amount = initial_amount;
        
        for route in routes {
            let fee_bps = self.get_dex_fee_bps(&route.dex);
            let fee = current_amount
                .checked_mul(fee_bps as u64)
                .ok_or(RouteError::MathOverflow)?
                .checked_div(10000)
                .ok_or(RouteError::DivisionByZero)?;
            
            total_fees = total_fees.checked_add(fee).ok_or(RouteError::MathOverflow)?;
            current_amount = current_amount.saturating_sub(fee);
        }
        
        Ok(total_fees)
    }
    
    /// 计算实际DEX费用
    fn calculate_actual_dex_fees(&self, amount_in: u64, amount_out: u64) -> Result<u64> {
        // 实际费用 = 输入金额 - 输出金额 - 滑点损失
        // 这里简化计算，假设差额主要来自DEX费用
        if amount_in > amount_out {
            // 考虑到这可能包含滑点，我们估算一个合理的费用比例
            let total_cost = amount_in.saturating_sub(amount_out);
            // 假设费用占总成本的70%，其余是滑点
            let estimated_fees = total_cost.checked_mul(70).unwrap_or(0).checked_div(100).unwrap_or(0);
            Ok(estimated_fees)
        } else {
            Ok(0) // 套利成功的情况
        }
    }
    
    /// 估算Gas成本
    fn estimate_gas_cost(&self, routes: &[Route]) -> Result<u64> {
        let base_gas = 50_000u64; // 基础Gas消耗
        let per_route_gas = 25_000u64; // 每个路由步骤的Gas
        
        let total_compute_units = base_gas + (routes.len() as u64 * per_route_gas);
        let gas_cost = total_compute_units
            .checked_mul(self.gas_price)
            .ok_or(RouteError::MathOverflow)?;
        
        Ok(gas_cost)
    }
    
    /// 估算总滑点损失
    fn estimate_total_slippage(&self, routes: &[Route], initial_amount: u64) -> Result<u64> {
        let mut total_slippage = 0u64;
        let mut current_amount = initial_amount;
        
        for route in routes {
            let slippage = self.estimate_slippage(current_amount, &route.dex)?;
            total_slippage = total_slippage.checked_add(slippage).ok_or(RouteError::MathOverflow)?;
            current_amount = current_amount.saturating_sub(slippage);
        }
        
        Ok(total_slippage)
    }
    
    /// 估算成功概率
    fn estimate_success_probability(&self, routes: &[Route]) -> Result<f64> {
        let base_probability = 0.90;
        let complexity_penalty = (routes.len() as f64 - 1.0) * 0.05;
        let final_probability = (base_probability - complexity_penalty).max(0.5);
        
        Ok(final_probability)
    }
    
    /// 应用风险调整
    fn apply_risk_adjustment(&self, profit: u64, routes: &[Route]) -> Result<u64> {
        let risk_factor = 1.0 - (routes.len() as f64 * 0.02); // 每增加一步降低2%
        let adjusted_profit = (profit as f64 * risk_factor) as u64;
        
        Ok(adjusted_profit)
    }
    
    /// 验证利润计算的合理性
    fn validate_profit_calculation(&self, gross_profit: u64, fees: &FeeBreakdown) -> Result<()> {
        // 检查费用是否合理
        if fees.total_fees > gross_profit {
            msg!("警告: 总费用 {} 超过毛利润 {}", fees.total_fees, gross_profit);
        }
        
        // 检查闪电贷费用是否过高
        if fees.flash_loan_fee > gross_profit / 2 {
            msg!("警告: 闪电贷费用过高: {}", fees.flash_loan_fee);
        }
        
        // 检查协议费用是否合理
        if fees.protocol_fee > gross_profit / 10 {
            msg!("警告: 协议费用过高: {}", fees.protocol_fee);
        }
        
        Ok(())
    }
    
    /// 设置Gas价格
    pub fn set_gas_price(&mut self, gas_price: u64) {
        self.gas_price = gas_price;
        msg!("Gas价格已更新为: {} lamports per CU", gas_price);
    }
    
    /// 设置协议费率
    pub fn set_protocol_fee_bps(&mut self, fee_bps: u16) {
        self.protocol_fee_bps = fee_bps;
        msg!("协议费率已更新为: {} bps", fee_bps);
    }
    
    /// 设置运营费率
    pub fn set_operator_fee_bps(&mut self, fee_bps: u16) {
        self.operator_fee_bps = fee_bps;
        msg!("运营费率已更新为: {} bps", fee_bps);
    }
}

/// 利润预估结果
#[derive(Debug, Clone)]
pub struct ProfitEstimate {
    /// 毛利润
    pub gross_profit: u64,
    /// 费用明细
    pub fee_breakdown: FeeBreakdown,
    /// 净利润
    pub net_profit: u64,
    /// 风险调整后利润
    pub risk_adjusted_profit: u64,
    /// 成功概率
    pub success_probability: f64,
    /// 预估输出
    pub estimated_output: u64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::routing::types::{Dex, RouteConfig};

    #[test]
    fn test_calculator_creation() {
        let config = super::super::ArbitrageConfig::default();
        let calculator = ArbitrageCalculator::new(config);
        
        assert_eq!(calculator.gas_price, 100);
        assert_eq!(calculator.protocol_fee_bps, 30);
        assert_eq!(calculator.operator_fee_bps, 20);
    }

    #[test]
    fn test_dex_fee_calculation() {
        let config = super::super::ArbitrageConfig::default();
        let calculator = ArbitrageCalculator::new(config);
        
        assert_eq!(calculator.get_dex_fee_bps(&Dex::RaydiumClmm), 25);
        assert_eq!(calculator.get_dex_fee_bps(&Dex::Orca), 30);
        assert_eq!(calculator.get_dex_fee_bps(&Dex::MeteoraAmm), 20);
        assert_eq!(calculator.get_dex_fee_bps(&Dex::PumpSwap), 100);
    }

    #[test]
    fn test_profit_distribution() {
        let config = super::super::ArbitrageConfig::default();
        let calculator = ArbitrageCalculator::new(config);
        
        let distribution = calculator.calculate_profit_distribution(
            1000000, // 0.001 SOL profit
            500000,  // 0.0005 SOL user stake
            1000000, // 0.001 SOL flash loan
        );
        
        assert!(distribution.is_ok());
        let dist = distribution.unwrap();
        assert!(dist.user_profit > 0);
        assert!(dist.protocol_profit > 0);
        assert!(dist.operator_profit > 0);
        assert_eq!(dist.user_profit + dist.protocol_profit + dist.operator_profit, dist.net_profit);
    }

    #[test]
    fn test_gas_cost_estimation() {
        let config = super::super::ArbitrageConfig::default();
        let calculator = ArbitrageCalculator::new(config);
        
        let routes = vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![],
                min_amount_out: 900,
            },
            Route {
                dex: Dex::Orca,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![],
                min_amount_out: 900,
            },
        ];
        
        let gas_cost = calculator.estimate_gas_cost(&routes);
        assert!(gas_cost.is_ok());
        assert!(gas_cost.unwrap() > 0);
    }
}