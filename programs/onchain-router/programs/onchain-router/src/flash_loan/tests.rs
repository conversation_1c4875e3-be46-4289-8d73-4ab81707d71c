//! 闪电贷模块测试
//! 
//! 为闪电贷功能提供全面的单元测试和集成测试

#[cfg(test)]
mod tests {
    use super::super::*;
    use crate::error::RouteError;
    use anchor_lang::prelude::*;
    use std::str::FromStr;

    // 测试用的模拟数据
    const SOL_MINT: &str = "So11111111111111111111111111111111111111112";
    const USDC_MINT: &str = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";

    /// 创建测试用的Pubkey
    fn test_pubkey() -> Pubkey {
        Pubkey::from_str("11111111111111111111111111111112").unwrap()
    }

    /// 创建测试用的SOL mint
    fn sol_mint() -> Pubkey {
        crate::flash_loan::kamino::SOL_MINT
    }

    /// 创建测试用的USDC mint
    fn usdc_mint() -> Pubkey {
        crate::flash_loan::kamino::USDC_MINT
    }

    mod flash_loan_stats_tests {
        use super::*;
        use crate::flash_loan::FlashLoanStats;

        #[test]
        fn test_flash_loan_stats_creation() {
            let stats = FlashLoanStats::default();
            
            assert_eq!(stats.total_executions, 0);
            assert_eq!(stats.successful_executions, 0);
            assert_eq!(stats.total_borrowed, 0);
            assert_eq!(stats.total_fees_paid, 0);
            assert_eq!(stats.total_profit, 0);
            assert_eq!(stats.average_execution_time_ms, 0);
        }

        #[test]
        fn test_success_rate_calculation() {
            let mut stats = FlashLoanStats::default();
            
            // 空状态下成功率为0
            assert_eq!(stats.success_rate(), 0.0);
            
            // 添加成功执行
            stats.update(true, 1000, 10, 50, 100);
            assert_eq!(stats.success_rate(), 1.0);
            
            // 添加失败执行
            stats.update(false, 0, 0, 0, 200);
            assert_eq!(stats.success_rate(), 0.5);
            
            // 再添加成功执行
            stats.update(true, 2000, 20, 100, 150);
            assert!((stats.success_rate() - (2.0 / 3.0)).abs() < f64::EPSILON);
        }

        #[test]
        fn test_average_fee_bps_calculation() {
            let mut stats = FlashLoanStats::default();
            
            // 空状态下费率为0
            assert_eq!(stats.average_fee_bps(), 0);
            
            // 添加执行记录（费率1%）
            stats.update(true, 10000, 100, 50, 100); // 1% fee
            assert_eq!(stats.average_fee_bps(), 100);
            
            // 添加另一个执行记录（费率0.5%）
            stats.update(true, 20000, 100, 100, 150); // 0.5% fee
            let expected = ((100 + 100) * 10000) / (10000 + 20000);
            assert_eq!(stats.average_fee_bps(), expected as u16);
        }

        #[test]
        fn test_stats_update() {
            let mut stats = FlashLoanStats::default();
            
            // 成功执行
            stats.update(true, 5000, 50, 200, 300);
            
            assert_eq!(stats.total_executions, 1);
            assert_eq!(stats.successful_executions, 1);
            assert_eq!(stats.total_borrowed, 5000);
            assert_eq!(stats.total_fees_paid, 50);
            assert_eq!(stats.total_profit, 200);
            assert_eq!(stats.average_execution_time_ms, 300);
            
            // 失败执行
            stats.update(false, 3000, 30, 0, 250);
            
            assert_eq!(stats.total_executions, 2);
            assert_eq!(stats.successful_executions, 1);
            assert_eq!(stats.total_borrowed, 5000); // 失败的不计入
            assert_eq!(stats.total_fees_paid, 50); // 失败的不计入
            assert_eq!(stats.total_profit, 200); // 失败的不计入
            assert_eq!(stats.average_execution_time_ms, (300 + 250) / 2);
        }
    }

    mod kamino_adapter_tests {
        use super::*;
        use crate::flash_loan::{KaminoFlashLoan, FlashLoanProvider};

        #[test]
        fn test_kamino_creation() {
            let kamino = KaminoFlashLoan::default();
            
            assert_eq!(kamino.get_provider_name(), "Kamino");
            assert_eq!(kamino.get_fee_bps(), 9); // 0.09%
            assert_eq!(kamino.supported_mints.len(), 3);
            assert!(kamino.supports_mint(&sol_mint()));
            assert!(kamino.supports_mint(&usdc_mint()));
        }

        #[test]
        fn test_fee_calculation() {
            let kamino = KaminoFlashLoan::default();
            
            // 测试1000 USDC的费用（假设6位小数）
            let amount = 1000 * 1_000_000;
            let expected_fee = amount * 9 / 10_000; // 0.09%
            
            let calculated_fee = kamino.calculate_fee(amount).unwrap();
            assert_eq!(calculated_fee, expected_fee);
        }

        #[test]
        fn test_fee_calculation_edge_cases() {
            let kamino = KaminoFlashLoan::default();
            
            // 测试小金额（确保最小费用为1）
            let small_amount = 10;
            let fee = kamino.calculate_fee(small_amount).unwrap();
            assert!(fee >= 1);
            
            // 测试零金额
            let zero_fee = kamino.calculate_fee(0).unwrap();
            assert_eq!(zero_fee, 1); // 最小费用
        }

        #[test]
        fn test_supports_mint() {
            let kamino = KaminoFlashLoan::default();
            
            // 支持的代币
            assert!(kamino.supports_mint(&sol_mint()));
            assert!(kamino.supports_mint(&usdc_mint()));
            
            // 不支持的代币
            let random_mint = test_pubkey();
            assert!(!kamino.supports_mint(&random_mint));
        }

        #[test]
        fn test_add_supported_mint() {
            let mut kamino = KaminoFlashLoan::default();
            let new_mint = test_pubkey();
            
            // 最初不支持
            assert!(!kamino.supports_mint(&new_mint));
            
            // 添加支持
            kamino.add_supported_mint(new_mint);
            assert!(kamino.supports_mint(&new_mint));
            
            // 重复添加不应该导致重复
            let original_count = kamino.supported_mints.len();
            kamino.add_supported_mint(new_mint);
            assert_eq!(kamino.supported_mints.len(), original_count);
        }

        #[test]
        fn test_set_fee_bps() {
            let mut kamino = KaminoFlashLoan::default();
            
            // 设置正常费率
            kamino.set_fee_bps(50); // 0.5%
            assert_eq!(kamino.get_fee_bps(), 50);
            
            // 设置超高费率（应该被限制）
            kamino.set_fee_bps(1000); // 10%
            assert_eq!(kamino.get_fee_bps(), 500); // 被限制为5%
        }

        #[test]
        fn test_max_loan_amount() {
            let kamino = KaminoFlashLoan::default();
            
            // SOL的最大借贷量
            let sol_max = kamino.get_max_loan_amount(&sol_mint()).unwrap();
            assert_eq!(sol_max, 10_000 * 1_000_000_000);
            
            // USDC的最大借贷量
            let usdc_max = kamino.get_max_loan_amount(&usdc_mint()).unwrap();
            assert_eq!(usdc_max, 1_000_000 * 1_000_000);
            
            // 不支持的代币
            let random_mint = test_pubkey();
            assert!(kamino.get_max_loan_amount(&random_mint).is_err());
        }

        #[test]
        fn test_validate_repayment() {
            let kamino = KaminoFlashLoan::default();
            
            let amount = 1000 * 1_000_000;
            let correct_fee = kamino.calculate_fee(amount).unwrap();
            
            // 正确的还款验证
            assert!(kamino.validate_repayment(amount, correct_fee).is_ok());
            
            // 错误的费用
            assert!(kamino.validate_repayment(amount, correct_fee + 1).is_err());
            assert!(kamino.validate_repayment(amount, correct_fee - 1).is_err());
            
            // 零金额
            assert!(kamino.validate_repayment(0, 0).is_err());
        }
    }

    mod manager_tests {
        use super::*;
        use crate::flash_loan::{FlashLoanManagerImpl, FlashLoanManager, ProviderInfo};

        #[test]
        fn test_manager_creation() {
            let manager = FlashLoanManagerImpl::default();
            
            // 默认包含Kamino提供者
            assert_eq!(manager.providers.len(), 1);
            assert_eq!(manager.provider_info.len(), 1);
            assert_eq!(manager.whitelist.len(), 1);
        }

        #[test]
        fn test_manager_with_config() {
            let manager = FlashLoanManagerImpl::new(200, false);
            
            assert_eq!(manager.max_fee_bps, 200);
            assert_eq!(manager.auto_failover, false);
            assert_eq!(manager.providers.len(), 1);
        }

        #[test]
        fn test_provider_info() {
            let manager = FlashLoanManagerImpl::default();
            
            // 检查Kamino提供者信息
            let kamino_id = crate::flash_loan::KAMINO_PROGRAM_ID;
            let info = manager.get_provider_info(&kamino_id);
            
            assert!(info.is_some());
            let info = info.unwrap();
            assert_eq!(info.name, "Kamino");
            assert!(info.enabled);
            assert_eq!(info.reliability_score, 95);
        }

        #[test]
        fn test_whitelist_management() {
            let manager = FlashLoanManagerImpl::default();
            
            // Kamino应该在白名单中
            let kamino_id = crate::flash_loan::KAMINO_PROGRAM_ID;
            assert!(manager.is_provider_whitelisted(&kamino_id));
            
            // 随机程序不在白名单中
            let random_id = test_pubkey();
            assert!(!manager.is_provider_whitelisted(&random_id));
        }

        #[test]
        fn test_set_max_fee() {
            let mut manager = FlashLoanManagerImpl::default();
            
            manager.set_max_fee_bps(150);
            assert_eq!(manager.max_fee_bps, 150);
        }

        #[test]
        fn test_set_auto_failover() {
            let mut manager = FlashLoanManagerImpl::default();
            
            manager.set_auto_failover(false);
            assert_eq!(manager.auto_failover, false);
        }
    }

    mod callback_tests {
        use super::*;
        use crate::flash_loan::{FlashLoanCallbackHandler, CallbackData};
        use crate::routing::types::Route;

        #[test]
        fn test_callback_handler_creation() {
            let handler = FlashLoanCallbackHandler::default();
            
            assert_eq!(handler.max_steps, 6);
            assert_eq!(handler.max_slippage_bps, 300);
            assert_eq!(handler.verbose_logging, true);
        }

        #[test]
        fn test_callback_handler_custom() {
            let handler = FlashLoanCallbackHandler::new(4, 200, false);
            
            assert_eq!(handler.max_steps, 4);
            assert_eq!(handler.max_slippage_bps, 200);
            assert_eq!(handler.verbose_logging, false);
        }

        // 注意：由于CallbackData需要Clock::get()，实际测试需要在Solana环境中运行
        // 这里主要测试数据结构和基本逻辑
        
        #[test]
        fn test_callback_data_basic_validation() {
            // 这个测试演示了如何验证回调数据结构
            // 实际测试需要mock Clock或在集成测试中进行
            
            let route = Route {
                dex: crate::routing::types::Dex::Raydium, // 使用已存在的枚举
                input_mint: sol_mint(),
                output_mint: usdc_mint(),
                swap_data: vec![1, 2, 3, 4],
                min_amount_out: 1000,
            };
            
            // 验证路由结构
            assert_eq!(route.input_mint, sol_mint());
            assert_eq!(route.output_mint, usdc_mint());
            assert!(!route.swap_data.is_empty());
        }
    }

    mod service_tests {
        use super::*;
        use crate::flash_loan::{FlashLoanService, FlashLoanTerms, ProviderTerms};

        #[test]
        fn test_service_creation() {
            let service = FlashLoanService::default();
            
            assert!(service.monitoring_enabled);
        }

        #[test]
        fn test_service_with_config() {
            let service = FlashLoanService::new(150, false);
            
            assert!(!service.monitoring_enabled);
        }

        #[test]
        fn test_flash_loan_terms() {
            let terms = FlashLoanTerms {
                provider_name: "TestProvider",
                program_id: test_pubkey(),
                max_amount: 1_000_000,
                fee: 1_000,
                fee_bps: 100,
                available_amount: 1_000_000,
            };
            
            assert_eq!(terms.provider_name, "TestProvider");
            assert_eq!(terms.fee_bps, 100);
            assert_eq!(terms.max_amount, 1_000_000);
        }

        #[test]
        fn test_provider_terms() {
            let terms = ProviderTerms {
                name: "TestProvider",
                program_id: test_pubkey(),
                max_amount: 500_000,
                fee_bps: 50,
                supported: true,
            };
            
            assert_eq!(terms.name, "TestProvider");
            assert!(terms.supported);
            assert_eq!(terms.fee_bps, 50);
        }
    }

    mod integration_tests {
        use super::*;
        use crate::flash_loan::{create_flash_loan_service, create_configured_flash_loan_service};

        #[test]
        fn test_create_default_service() {
            let service = create_flash_loan_service();
            assert!(service.monitoring_enabled);
        }

        #[test]
        fn test_create_configured_service() {
            let service = create_configured_flash_loan_service(80, false);
            assert!(!service.monitoring_enabled);
        }

        // 以下测试需要在实际Solana环境中运行，因为涉及Clock和账户操作
        /*
        #[test]
        fn test_flash_loan_execution_flow() {
            // 这个测试应该在集成测试中实现
            // 需要设置完整的账户结构和模拟交易环境
        }

        #[test]
        fn test_provider_selection_logic() {
            // 测试最优提供者选择逻辑
            // 需要模拟多个提供者和不同的市场条件
        }

        #[test]
        fn test_callback_execution() {
            // 测试回调执行流程
            // 需要模拟套利路由和账户状态
        }
        */
    }

    mod error_handling_tests {
        use super::*;
        use crate::error::RouteError;

        #[test]
        fn test_flash_loan_specific_errors() {
            // 测试闪电贷相关的错误类型
            let errors = vec![
                RouteError::InvalidFlashLoanAmount,
                RouteError::UnsupportedMint,
                RouteError::FlashLoanExecutionFailed,
                RouteError::IncorrectFlashLoanFee,
                RouteError::ProviderNotFound,
                RouteError::NoSuitableProvider,
                RouteError::ArbitrageExecutionFailed,
                RouteError::InsufficientProfit,
                RouteError::InvalidCallbackData,
                RouteError::CallbackTimeout,
            ];

            for error in errors {
                // 验证错误代码在正确的范围内
                let error_code = error as u32;
                assert!(error_code >= 6210 && error_code <= 6231, 
                    "错误代码 {} 不在预期范围内", error_code);
            }
        }

        #[test]
        fn test_error_recovery_actions() {
            use crate::error::{ErrorRecovery, ErrorRecoveryAction};
            
            // 测试闪电贷提供者不可用的恢复策略
            let error = RouteError::FlashLoanProviderUnavailable;
            assert!(error.can_retry());
            assert_eq!(
                error.get_recovery_action(0), 
                Some(ErrorRecoveryAction::SwitchDex)
            );
            
            // 测试套利执行失败的恢复策略
            let error = RouteError::ArbitrageExecutionFailed;
            assert_eq!(
                error.get_recovery_action(0),
                Some(ErrorRecoveryAction::UseAlternativeRoute)
            );
            
            // 测试致命错误（无法恢复）
            let error = RouteError::InvalidFlashLoanAmount;
            assert!(!error.can_retry());
            assert_eq!(error.get_recovery_action(0), None);
        }
    }
}

// 基准测试（需要 cargo bench 运行）
#[cfg(test)]
mod benchmarks {
    use super::*;

    // 注意：Solana程序通常不包含基准测试
    // 这些测试主要用于开发时的性能分析
    
    /*
    use test::Bencher;

    #[bench]
    fn bench_fee_calculation(b: &mut Bencher) {
        let kamino = crate::flash_loan::KaminoFlashLoan::default();
        let amount = 1_000_000 * 1_000_000; // 1M tokens
        
        b.iter(|| {
            kamino.calculate_fee(amount).unwrap()
        });
    }

    #[bench]
    fn bench_provider_selection(b: &mut Bencher) {
        let manager = crate::flash_loan::FlashLoanManagerImpl::default();
        let amount = 1_000_000;
        let mint = sol_mint();
        
        b.iter(|| {
            // 这个测试需要在Solana环境中运行
            // manager.get_best_provider(amount, &mint)
        });
    }
    */
}

// 模拟数据生成器（用于测试）
#[cfg(test)]
pub mod test_utils {
    use super::*;
    use crate::flash_loan::{FlashLoanStats, ProviderInfo};

    /// 创建测试用的统计数据
    pub fn create_test_stats(
        total: u64,
        successful: u64,
        borrowed: u64,
        fees: u64,
        profit: u64,
    ) -> FlashLoanStats {
        let mut stats = FlashLoanStats::default();
        stats.total_executions = total;
        stats.successful_executions = successful;
        stats.total_borrowed = borrowed;
        stats.total_fees_paid = fees;
        stats.total_profit = profit;
        stats
    }

    /// 创建测试用的提供者信息
    pub fn create_test_provider_info(
        name: &'static str,
        enabled: bool,
        reliability: u8,
    ) -> ProviderInfo {
        ProviderInfo {
            name,
            program_id: test_pubkey(),
            enabled,
            reliability_score: reliability,
            stats: FlashLoanStats::default(),
        }
    }

    /// 创建测试用的路由配置
    pub fn create_test_route() -> crate::routing::types::Route {
        crate::routing::types::Route {
            dex: crate::routing::types::Dex::Raydium,
            input_mint: sol_mint(),
            output_mint: usdc_mint(),
            swap_data: vec![0x01, 0x02, 0x03, 0x04],
            min_amount_out: 1000,
        }
    }

    /// 生成测试用的随机金额
    pub fn random_amount(min: u64, max: u64) -> u64 {
        // 简单的伪随机数生成（测试用）
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        std::ptr::addr_of!(min).hash(&mut hasher);
        let hash = hasher.finish();
        
        min + (hash % (max - min + 1))
    }
}