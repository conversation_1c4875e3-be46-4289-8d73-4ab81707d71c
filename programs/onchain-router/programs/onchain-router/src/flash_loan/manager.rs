//! 闪电贷管理器
//! 
//! 统一管理多个闪电贷提供者，提供最优费率选择和风险管理

use anchor_lang::prelude::*;
use std::collections::HashMap;
use crate::error::RouteError;
use super::traits::{<PERSON><PERSON><PERSON>Provider, FlashLoanManager, FlashLoanStats};
use super::kamino::KaminoFlashLoan;

/// 闪电贷提供者信息
#[derive(Debug, Clone)]
pub struct ProviderInfo {
    /// 提供者名称
    pub name: &'static str,
    /// 程序ID
    pub program_id: Pubkey,
    /// 是否启用
    pub enabled: bool,
    /// 可靠性评分（0-100）
    pub reliability_score: u8,
    /// 执行统计
    pub stats: FlashLoanStats,
}

/// 闪电贷管理器实现
pub struct FlashLoanManagerImpl {
    /// 注册的提供者
    providers: Vec<Box<dyn FlashLoanProvider>>,
    /// 提供者信息映射
    provider_info: HashMap<Pubkey, ProviderInfo>,
    /// 白名单程序ID
    whitelist: Vec<Pubkey>,
    /// 默认费率限制（基点）
    max_fee_bps: u16,
    /// 是否启用自动故障转移
    auto_failover: bool,
}

impl Default for FlashLoanManagerImpl {
    fn default() -> Self {
        Self {
            providers: Vec::new(),
            provider_info: HashMap::new(),
            whitelist: Vec::new(),
            max_fee_bps: 100, // 默认最大1%费率
            auto_failover: true,
        }
    }
}

impl FlashLoanManagerImpl {
    /// 创建新的闪电贷管理器
    pub fn new(max_fee_bps: u16, auto_failover: bool) -> Self {
        let mut manager = Self {
            providers: Vec::new(),
            provider_info: HashMap::new(),
            whitelist: Vec::new(),
            max_fee_bps,
            auto_failover,
        };
        
        // 添加默认的Kamino提供者
        manager.add_kamino_provider();
        
        manager
    }
    
    /// 添加Kamino提供者
    fn add_kamino_provider(&mut self) {
        let kamino = Box::new(KaminoFlashLoan::default());
        let program_id = kamino.get_program_id();
        
        // 添加到白名单
        self.whitelist.push(program_id);
        
        // 添加提供者信息
        let info = ProviderInfo {
            name: kamino.get_provider_name(),
            program_id,
            enabled: true,
            reliability_score: 95, // Kamino是可靠的协议
            stats: FlashLoanStats::default(),
        };
        
        self.provider_info.insert(program_id, info);
        self.providers.push(kamino);
        
        msg!("已添加Kamino闪电贷提供者");
    }
    
    /// 添加自定义提供者
    pub fn add_provider(&mut self, provider: Box<dyn FlashLoanProvider>) -> Result<()> {
        let program_id = provider.get_program_id();
        
        // 检查是否已存在
        if self.provider_info.contains_key(&program_id) {
            return Err(RouteError::ProviderAlreadyExists.into());
        }
        
        // 添加到白名单
        if !self.whitelist.contains(&program_id) {
            self.whitelist.push(program_id);
        }
        
        // 添加提供者信息
        let info = ProviderInfo {
            name: provider.get_provider_name(),
            program_id,
            enabled: true,
            reliability_score: 80, // 新提供者默认较低评分
            stats: FlashLoanStats::default(),
        };
        
        let provider_name = info.name;
        self.provider_info.insert(program_id, info);
        self.providers.push(provider);
        
        msg!("已添加闪电贷提供者: {}", provider_name);
        Ok(())
    }
    
    /// 移除提供者
    pub fn remove_provider(&mut self, program_id: &Pubkey) -> Result<()> {
        // 从提供者列表中移除
        let index = self.providers
            .iter()
            .position(|p| p.get_program_id() == *program_id)
            .ok_or(RouteError::ProviderNotFound)?;
        
        self.providers.remove(index);
        self.provider_info.remove(program_id);
        
        // 从白名单中移除
        if let Some(pos) = self.whitelist.iter().position(|&x| x == *program_id) {
            self.whitelist.remove(pos);
        }
        
        msg!("已移除闪电贷提供者: {}", program_id);
        Ok(())
    }
    
    /// 启用/禁用提供者
    pub fn set_provider_enabled(&mut self, program_id: &Pubkey, enabled: bool) -> Result<()> {
        let info = self.provider_info
            .get_mut(program_id)
            .ok_or(RouteError::ProviderNotFound)?;
        
        info.enabled = enabled;
        msg!("提供者 {} 已{}", info.name, if enabled { "启用" } else { "禁用" });
        Ok(())
    }
    
    /// 更新提供者可靠性评分
    pub fn update_reliability_score(&mut self, program_id: &Pubkey, score: u8) -> Result<()> {
        let info = self.provider_info
            .get_mut(program_id)
            .ok_or(RouteError::ProviderNotFound)?;
        
        info.reliability_score = score.min(100);
        msg!("提供者 {} 可靠性评分更新为: {}", info.name, info.reliability_score);
        Ok(())
    }
    
    /// 更新提供者统计信息
    pub fn update_provider_stats(
        &mut self,
        program_id: &Pubkey,
        success: bool,
        amount: u64,
        fee: u64,
        profit: u64,
        execution_time_ms: u64,
    ) -> Result<()> {
        let info = self.provider_info
            .get_mut(program_id)
            .ok_or(RouteError::ProviderNotFound)?;
        
        info.stats.update(success, amount, fee, profit, execution_time_ms);
        
        // 根据成功率调整可靠性评分
        let success_rate = info.stats.success_rate();
        if success_rate < 0.8 && info.reliability_score > 50 {
            info.reliability_score = ((info.reliability_score as f64) * 0.95) as u8;
        } else if success_rate > 0.95 && info.reliability_score < 95 {
            info.reliability_score = ((info.reliability_score as f64) * 1.05) as u8;
        }
        
        Ok(())
    }
    
    /// 计算提供者评分（综合费率、可靠性和性能）
    fn calculate_provider_score(&self, provider: &dyn FlashLoanProvider, amount: u64) -> Result<f64> {
        let program_id = provider.get_program_id();
        let info = self.provider_info
            .get(&program_id)
            .ok_or(RouteError::ProviderNotFound)?;
        
        if !info.enabled {
            return Ok(0.0); // 禁用的提供者评分为0
        }
        
        // 费率评分（费率越低评分越高）
        let fee_bps = provider.get_fee_bps();
        let fee_score = if fee_bps == 0 { 100.0 } else { 100.0 / (fee_bps as f64) };
        
        // 可靠性评分
        let reliability_score = info.reliability_score as f64;
        
        // 性能评分（基于历史统计）
        let performance_score = if info.stats.total_executions > 0 {
            let success_rate = info.stats.success_rate();
            let avg_time = info.stats.average_execution_time_ms as f64;
            let time_factor = if avg_time > 0.0 { 1000.0 / avg_time } else { 1.0 };
            success_rate * 100.0 * time_factor.min(2.0)
        } else {
            50.0 // 新提供者默认中等性能评分
        };
        
        // 加权综合评分
        let total_score = fee_score * 0.4 + reliability_score * 0.4 + performance_score * 0.2;
        
        Ok(total_score)
    }
    
    /// 获取提供者信息
    pub fn get_provider_info(&self, program_id: &Pubkey) -> Option<&ProviderInfo> {
        self.provider_info.get(program_id)
    }
    
    /// 获取所有提供者信息
    pub fn get_all_provider_info(&self) -> Vec<&ProviderInfo> {
        self.provider_info.values().collect()
    }
    
    /// 设置最大费率限制
    pub fn set_max_fee_bps(&mut self, max_fee_bps: u16) {
        self.max_fee_bps = max_fee_bps;
        msg!("最大费率限制设置为: {} bps", max_fee_bps);
    }
    
    /// 设置自动故障转移
    pub fn set_auto_failover(&mut self, enabled: bool) {
        self.auto_failover = enabled;
        msg!("自动故障转移已{}", if enabled { "启用" } else { "禁用" });
    }
}

impl FlashLoanManager for FlashLoanManagerImpl {
    fn get_best_provider(&self, amount: u64, mint: &Pubkey) -> Result<Box<dyn FlashLoanProvider>> {
        if self.providers.is_empty() {
            return Err(RouteError::NoAvailableProvider.into());
        }
        
        let mut best_provider: Option<&Box<dyn FlashLoanProvider>> = None;
        let mut best_score = 0.0;
        
        // 遍历所有提供者，找到最优的
        for provider in &self.providers {
            // 检查是否支持该代币
            if !provider.supports_mint(mint) {
                continue;
            }
            
            // 检查是否有足够的流动性
            let max_amount = match provider.get_max_loan_amount(mint) {
                Ok(max) => max,
                Err(_) => continue,
            };
            
            if amount > max_amount {
                continue;
            }
            
            // 检查费率是否在限制范围内
            if provider.get_fee_bps() > self.max_fee_bps {
                continue;
            }
            
            // 计算综合评分
            let score = match self.calculate_provider_score(provider.as_ref(), amount) {
                Ok(s) => s,
                Err(_) => continue,
            };
            
            if score > best_score {
                best_score = score;
                best_provider = Some(provider);
            }
        }
        
        match best_provider {
            Some(provider) => {
                msg!("选择最优闪电贷提供者: {} (评分: {:.2})", 
                    provider.get_provider_name(), best_score);
                
                // 由于trait object不能clone，这里需要创建新实例
                // 实际实现中可能需要不同的策略
                match provider.get_provider_name() {
                    "Kamino" => Ok(Box::new(KaminoFlashLoan::default())),
                    _ => Err(RouteError::UnsupportedProvider.into()),
                }
            }
            None => Err(RouteError::NoSuitableProvider.into()),
        }
    }
    
    fn get_providers_for_mint(&self, mint: &Pubkey) -> Vec<Box<dyn FlashLoanProvider>> {
        let mut suitable_providers = Vec::new();
        
        for provider in &self.providers {
            if provider.supports_mint(mint) {
                // 同样需要创建新实例
                match provider.get_provider_name() {
                    "Kamino" => suitable_providers.push(Box::new(KaminoFlashLoan::default()) as Box<dyn FlashLoanProvider>),
                    _ => continue,
                }
            }
        }
        
        suitable_providers
    }
    
    fn get_all_providers(&self) -> Vec<Box<dyn FlashLoanProvider>> {
        let mut all_providers = Vec::new();
        
        for provider in &self.providers {
            match provider.get_provider_name() {
                "Kamino" => all_providers.push(Box::new(KaminoFlashLoan::default()) as Box<dyn FlashLoanProvider>),
                _ => continue,
            }
        }
        
        all_providers
    }
    
    fn is_provider_whitelisted(&self, program_id: &Pubkey) -> bool {
        self.whitelist.contains(program_id)
    }
}

/// 提供者性能监控器
pub struct ProviderMonitor {
    /// 监控的时间窗口（秒）
    window_seconds: i64,
    /// 性能阈值
    min_success_rate: f64,
    /// 最大平均响应时间（毫秒）
    max_avg_response_time_ms: u64,
}

impl ProviderMonitor {
    pub fn new(window_seconds: i64, min_success_rate: f64, max_avg_response_time_ms: u64) -> Self {
        Self {
            window_seconds,
            min_success_rate,
            max_avg_response_time_ms,
        }
    }
    
    /// 检查提供者性能是否符合要求
    pub fn check_provider_health(&self, info: &ProviderInfo) -> bool {
        // 检查成功率
        if info.stats.success_rate() < self.min_success_rate {
            return false;
        }
        
        // 检查平均响应时间
        if info.stats.average_execution_time_ms > self.max_avg_response_time_ms {
            return false;
        }
        
        // 检查可靠性评分
        if info.reliability_score < 50 {
            return false;
        }
        
        true
    }
    
    /// 建议的提供者操作
    pub fn suggest_action(&self, info: &ProviderInfo) -> ProviderAction {
        if !info.enabled {
            return ProviderAction::Keep;
        }
        
        if !self.check_provider_health(info) {
            if info.stats.total_executions < 10 {
                ProviderAction::Monitor // 样本太少，继续监控
            } else {
                ProviderAction::Disable // 性能不佳，建议禁用
            }
        } else if info.stats.success_rate() > 0.98 && info.reliability_score > 90 {
            ProviderAction::Promote // 性能优秀，建议提升优先级
        } else {
            ProviderAction::Keep
        }
    }
}

/// 提供者操作建议
#[derive(Debug, Clone, PartialEq)]
pub enum ProviderAction {
    /// 保持当前状态
    Keep,
    /// 继续监控
    Monitor,
    /// 禁用提供者
    Disable,
    /// 提升优先级
    Promote,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_manager_creation() {
        let manager = FlashLoanManagerImpl::new(100, true);
        assert_eq!(manager.max_fee_bps, 100);
        assert_eq!(manager.auto_failover, true);
        assert_eq!(manager.providers.len(), 1); // 默认包含Kamino
    }
    
    #[test]
    fn test_provider_info() {
        let manager = FlashLoanManagerImpl::default();
        let info = ProviderInfo {
            name: "Test",
            program_id: Pubkey::new_unique(),
            enabled: true,
            reliability_score: 85,
            stats: FlashLoanStats::default(),
        };
        
        assert_eq!(info.name, "Test");
        assert_eq!(info.enabled, true);
        assert_eq!(info.reliability_score, 85);
    }
    
    #[test]
    fn test_provider_monitor() {
        let monitor = ProviderMonitor::new(3600, 0.9, 1000);
        
        let mut good_info = ProviderInfo {
            name: "Good",
            program_id: Pubkey::new_unique(),
            enabled: true,
            reliability_score: 95,
            stats: FlashLoanStats::default(),
        };
        
        // 模拟好的统计数据
        for _ in 0..20 {
            good_info.stats.update(true, 1000, 10, 50, 500);
        }
        
        assert!(monitor.check_provider_health(&good_info));
        assert_eq!(monitor.suggest_action(&good_info), ProviderAction::Promote);
    }
}