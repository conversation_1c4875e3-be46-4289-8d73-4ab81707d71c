//! 分支路由执行指令
//! 
//! 处理分散-聚合模式的路由执行

use anchor_lang::prelude::*;
use anchor_spl::token::{Token, TokenAccount};
use anchor_spl::token_interface::{Mint, TokenInterface};
use crate::state::{RouterConfig, UserPosition};
use crate::routing::types::BranchRouteConfig;
use crate::processor::RouteProcessor;

/// 执行分支路由的账户结构
#[derive(Accounts)]
pub struct ExecuteBranchRouteAccounts<'info> {
    #[account(mut)]
    pub user: Signer<'info>,
    
    #[account(
        seeds = [b"config"],
        bump,
        constraint = !config.emergency_stop @ crate::error::RouteError::GlobalEmergencyStop
    )]
    pub config: Account<'info, RouterConfig>,
    
    #[account(
        mut,
        seeds = [b"position", user.key().as_ref()],
        bump,
        constraint = !user_position.is_suspended @ crate::error::RouteError::UserSuspended
    )]
    pub user_position: Account<'info, UserPosition>,
    
    /// 源代币账户（用于分散）
    #[account(
        mut,
        token::authority = user,
        constraint = source_token_account.amount > 0 @ crate::error::RouteError::InsufficientBalance
    )]
    pub source_token_account: Account<'info, TokenAccount>,
    
    /// 目标代币账户（用于聚合）
    #[account(
        mut,
        token::authority = user
    )]
    pub target_token_account: Account<'info, TokenAccount>,
    
    /// 源代币mint
    pub source_mint: InterfaceAccount<'info, Mint>,
    
    /// 目标代币mint
    pub target_mint: InterfaceAccount<'info, Mint>,
    
    /// Token程序
    pub token_program: Program<'info, Token>,
    
    /// Token2022程序（可选）
    pub token_2022_program: Option<Interface<'info, TokenInterface>>,
    
    /// 系统程序
    pub system_program: Program<'info, System>,
}

/// 执行分支路由处理器
pub fn execute_branch_route_handler<'a>(
    ctx: Context<'_, '_, 'a, 'a, ExecuteBranchRouteAccounts<'a>>,
    branch_config: BranchRouteConfig,
    order_id: u64,
) -> Result<()> {
    msg!("开始执行分支路由 - 用户: {}, 订单ID: {}", ctx.accounts.user.key(), order_id);
    
    // 1. 验证分支配置
    validate_branch_config(&branch_config, &ctx.accounts)?;
    
    // 2. 验证输入分配比例总和为100%
    let total_distribution: u32 = branch_config.input_distribution.iter().map(|&x| x as u32).sum();
    if total_distribution != 10000 {
        return Err(crate::error::RouteError::InvalidRouteConfig.into());
    }
    
    // 3. 验证分支数量匹配
    if branch_config.input_distribution.len() != branch_config.branch_routes.len() {
        return Err(crate::error::RouteError::InvalidRouteConfig.into());
    }
    
    // 4. 记录执行前状态
    let start_time = Clock::get()?.unix_timestamp;
    let total_input = ctx.accounts.source_token_account.amount;
    
    // 5. 执行分支路由
    let result = RouteProcessor::execute_branch_route(
        ctx.accounts,
        branch_config.clone(),
        ctx.remaining_accounts,
        order_id,
    )?;
    
    // 6. 更新用户统计
    let user_position = &mut ctx.accounts.user_position;
    user_position.total_volume += total_input;
    user_position.successful_routes += 1;
    user_position.last_activity = Clock::get()?.unix_timestamp;
    
    // 7. 发出分支路由执行完成事件
    emit!(BranchRouteExecutedEvent {
        user: ctx.accounts.user.key(),
        order_id,
        total_input,
        total_output: result.total_output,
        branch_count: branch_config.branch_routes.len() as u8,
        average_slippage_bps: result.average_slippage_bps,
        execution_time: Clock::get()?.unix_timestamp - start_time,
        timestamp: Clock::get()?.unix_timestamp,
    });
    
    msg!("分支路由执行完成 - 输入: {}, 输出: {}, 分支数: {}", 
        total_input, result.total_output, branch_config.branch_routes.len());
    Ok(())
}

/// 验证分支配置的有效性
fn validate_branch_config(
    branch_config: &BranchRouteConfig, 
    accounts: &ExecuteBranchRouteAccounts
) -> Result<()> {
    // 验证目标代币mint匹配
    if branch_config.target_mint != accounts.target_mint.key() {
        return Err(crate::error::RouteError::InvalidRouteConfig.into());
    }
    
    // 验证每个分支路由的最终输出都是目标代币
    for branch_routes in &branch_config.branch_routes {
        if let Some(last_route) = branch_routes.last() {
            if last_route.output_mint != branch_config.target_mint {
                return Err(crate::error::RouteError::RouteDiscontinuity.into());
            }
        }
    }
    
    // 验证分支数量限制（最多8个分支）
    if branch_config.branch_routes.len() > 8 {
        return Err(crate::error::RouteError::RoutePathTooLong.into());
    }
    
    // 验证每个分支路由的长度限制
    for branch_routes in &branch_config.branch_routes {
        if branch_routes.len() > 4 {
            return Err(crate::error::RouteError::RoutePathTooLong.into());
        }
    }
    
    Ok(())
}

/// 分支路由执行成功事件
#[event]
pub struct BranchRouteExecutedEvent {
    pub user: Pubkey,
    pub order_id: u64,
    pub total_input: u64,
    pub total_output: u64,
    pub branch_count: u8,
    pub average_slippage_bps: u16,
    pub execution_time: i64,
    pub timestamp: i64,
}

/// 分支路由执行失败事件
#[event]
pub struct BranchRouteFailedEvent {
    pub user: Pubkey,
    pub order_id: u64,
    pub error_code: u32,
    pub failed_branch: u8,
    pub total_input: u64,
    pub amount_at_failure: u64,
    pub timestamp: i64,
}