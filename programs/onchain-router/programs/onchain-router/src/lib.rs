use anchor_lang::prelude::*;

// 程序ID设置为文档中指定的ID
declare_id!("11111111111111111111111111111112");

// 导入所有模块
pub mod adapters;
pub mod processor;
pub mod instructions;
pub mod state;
pub mod utils;
pub mod routing;
pub mod error;
pub mod constants;
pub mod flash_loan;
pub mod arbitrage;

// 重新导出错误类型
pub use error::*;

#[program]
pub mod onchain_router {
    use super::*;

    /// 初始化路由器配置
    /// 设置全局配置参数，如支持的DEX列表、费率等
    pub fn initialize_config(
        ctx: Context<instructions::InitializeConfig>,
        config_data: instructions::ConfigArgs,
    ) -> Result<()> {
        instructions::initialize_config_handler(ctx, config_data)
    }
    
    /// 初始化用户位置
    pub fn initialize_user_position(
        ctx: Context<instructions::InitializeUserPosition>,
    ) -> Result<()> {
        instructions::initialize_user_position_handler(ctx)
    }

    /// 执行通用路由
    /// 支持线性路由、循环路由（套利）、分支路由等多种模式
    pub fn execute_route<'a>(
        ctx: Context<'_, '_, 'a, 'a, instructions::ExecuteRouteAccounts<'a>>,
        route_config: routing::types::RouteConfig,
        order_id: u64,
    ) -> Result<()> {
        instructions::execute_route_handler(ctx, route_config, order_id)
    }

    /// 执行闪电贷路由（零本金套利）
    /// 通过闪电贷实现零本金的套利操作
    pub fn flash_loan_arbitrage<'a>(
        ctx: Context<'_, '_, 'a, 'a, instructions::FlashLoanRouteAccounts<'a>>,
        flash_loan_config: routing::types::FlashLoanRouteConfig,
        order_id: u64,
    ) -> Result<()> {
        instructions::flash_loan_route_handler(ctx, flash_loan_config, order_id)
    }

    /// 执行增强版闪电贷路由（使用多协议适配器）
    /// 自动选择最优闪电贷提供者并执行套利
    pub fn enhanced_flash_loan_arbitrage<'a>(
        ctx: Context<'_, '_, 'a, 'a, instructions::FlashLoanRouteAccounts<'a>>,
        flash_loan_config: routing::types::FlashLoanRouteConfig,
        order_id: u64,
        callback_data: Vec<u8>,
    ) -> Result<()> {
        instructions::enhanced_flash_loan_route_handler(ctx, flash_loan_config, order_id, callback_data)
    }

    /// 执行完整的零本金套利（完整版套利系统）
    /// 集成风险评估、利润计算、分配系统的完整套利功能
    pub fn execute_complete_flash_loan_arbitrage<'a>(
        ctx: Context<'_, '_, 'a, 'a, instructions::FlashLoanArbitrageAccounts<'a>>,
        path: arbitrage::OnchainArbitragePath,
        flash_amount: u64,
    ) -> Result<()> {
        instructions::execute_flash_loan_arbitrage(ctx, path, flash_amount)
    }

    /// 执行分支路由（分散-聚合模式）
    /// 将输入分散到多个路径，然后聚合到目标代币
    pub fn execute_branch_route<'a>(
        ctx: Context<'_, '_, 'a, 'a, instructions::ExecuteBranchRouteAccounts<'a>>,
        branch_config: routing::types::BranchRouteConfig,
        order_id: u64,
    ) -> Result<()> {
        instructions::execute_branch_route_handler(ctx, branch_config, order_id)
    }

    /// 执行批量路由（并行处理多个独立路由）
    /// 同时处理多个独立的路由操作，可选择原子性执行
    pub fn execute_batch_routes<'a>(
        ctx: Context<'_, '_, 'a, 'a, instructions::ExecuteBatchRoutesAccounts<'a>>,
        batch_config: routing::types::BatchRouteConfig,
        order_id: u64,
    ) -> Result<()> {
        instructions::execute_batch_routes_handler(ctx, batch_config, order_id)
    }

    /// 紧急停止功能
    /// 管理员可以在紧急情况下停止所有操作
    pub fn emergency_stop(
        ctx: Context<instructions::EmergencyStop>,
        stop_global: bool,
        stop_dexes: Vec<routing::types::Dex>,
    ) -> Result<()> {
        instructions::emergency_stop_handler(ctx, stop_global, stop_dexes)
    }
}