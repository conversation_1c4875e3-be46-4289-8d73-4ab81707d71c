//! 验证工具
//!
//! 提供各种验证函数，确保数据和操作的正确性

use anchor_lang::prelude::*;
use anchor_spl::token::TokenAccount;
use crate::error::RouteError;
use crate::routing::{Route, RouteConfig, Dex, RoutingMode};
use crate::state::{RouterConfig, UserPosition};
use std::collections::HashMap;

/// 验证路由路径的连续性
pub fn validate_route_continuity(routes: &[Route]) -> Result<()> {
    if routes.is_empty() {
        return Err(RouteError::EmptyRoutePath.into());
    }

    for i in 0..routes.len().saturating_sub(1) {
        if routes[i].output_mint != routes[i + 1].input_mint {
            msg!(
                "路由不连续：步骤{}输出mint {} != 步骤{}输入mint {}",
                i,
                routes[i].output_mint,
                i + 1,
                routes[i + 1].input_mint
            );
            return Err(RouteError::RouteDiscontinuity.into());
        }
    }

    Ok(())
}

/// 验证循环路由（套利）
pub fn validate_circular_route(routes: &[Route]) -> Result<()> {
    validate_route_continuity(routes)?;

    if routes.len() < 2 {
        return Err(RouteError::InvalidRouteConfig.into());
    }

    let start_mint = routes[0].input_mint;
    let end_mint = routes[routes.len() - 1].output_mint;

    if start_mint != end_mint {
        msg!(
            "循环路由验证失败：起始mint {} != 结束mint {}",
            start_mint,
            end_mint
        );
        return Err(RouteError::NotCircularRoute.into());
    }

    Ok(())
}

/// 验证代币账户的基本属性
pub fn validate_token_account(
    token_account: &AccountInfo,
    expected_mint: &Pubkey,
    expected_owner: &Pubkey,
    allow_zero_balance: bool,
) -> Result<()> {
    // 检查账户是否已初始化
    if token_account.data_is_empty() {
        return Err(RouteError::InvalidDexAccounts.into());
    }

    // 解析代币账户数据
    let token_data = TokenAccount::try_deserialize(
        &mut token_account.data.borrow().as_ref()
    ).map_err(|_| RouteError::InvalidDexAccounts)?;

    // 验证mint地址
    if token_data.mint != *expected_mint {
        msg!("代币mint不匹配: 期望 {}, 实际 {}", expected_mint, token_data.mint);
        return Err(RouteError::TokenAccountMintMismatch.into());
    }

    // 验证所有者
    if token_data.owner != *expected_owner {
        msg!("代币账户所有者不匹配: 期望 {}, 实际 {}", expected_owner, token_data.owner);
        return Err(RouteError::TokenAccountOwnerMismatch.into());
    }

    // 验证账户状态
    if token_data.state != anchor_spl::token::spl_token::state::AccountState::Initialized {
        return Err(RouteError::InvalidDexAccounts.into());
    }

    // 验证余额（如果需要）
    if !allow_zero_balance && token_data.amount == 0 {
        return Err(RouteError::InsufficientBalance.into());
    }

    Ok(())
}

/// 验证DEX是否受支持
pub fn validate_supported_dex(dex: &Dex, supported_dexes: &[Dex]) -> Result<()> {
    if !supported_dexes.contains(dex) {
        msg!("不支持的DEX: {:?}", dex);
        return Err(RouteError::UnsupportedDex.into());
    }
    Ok(())
}

/// 验证金额范围
pub fn validate_amount_range(
    amount: u64,
    min_amount: u64,
    max_amount: u64,
) -> Result<()> {
    if amount < min_amount {
        msg!("金额过小：{} < {}", amount, min_amount);
        return Err(RouteError::AmountValidationFailed.into());
    }

    if amount > max_amount {
        msg!("金额过大：{} > {}", amount, max_amount);
        return Err(RouteError::AmountValidationFailed.into());
    }

    Ok(())
}

/// 验证滑点设置
pub fn validate_slippage(
    slippage_bps: u16,
    max_allowed_bps: u16,
) -> Result<()> {
    if slippage_bps > max_allowed_bps {
        msg!(
            "滑点设置过高：{}基点 > {}基点",
            slippage_bps,
            max_allowed_bps
        );
        return Err(RouteError::SlippageTooHigh.into());
    }

    Ok(())
}

/// 验证路由复杂度
pub fn validate_route_complexity(
    routes: &[Route],
    max_steps: usize,
    max_complexity_score: u8,
) -> Result<()> {
    // 检查步骤数
    if routes.len() > max_steps {
        msg!(
            "路由步骤过多：{} > {}",
            routes.len(),
            max_steps
        );
        return Err(RouteError::RoutePathTooLong.into());
    }

    // 计算复杂度分数
    let complexity = calculate_route_complexity(routes);
    if complexity > max_complexity_score {
        msg!(
            "路由复杂度过高：{} > {}",
            complexity,
            max_complexity_score
        );
        return Err(RouteError::RoutePathTooLong.into());
    }

    Ok(())
}

/// 计算路由复杂度分数
pub fn calculate_route_complexity(routes: &[Route]) -> u8 {
    let base_score = routes.len() as u8;

    // 计算DEX多样性奖励
    let mut unique_dexes = std::collections::HashSet::new();
    for route in routes {
        unique_dexes.insert(&route.dex);
    }
    let diversity_score = unique_dexes.len() as u8;

    // 计算DEX复杂度权重
    let dex_complexity: u8 = routes.iter().map(|route| {
        match route.dex {
            Dex::RaydiumClmm => 3,    // CLMM较复杂
            Dex::MeteoraLb => 3,      // DLMM较复杂
            Dex::RaydiumCpmm => 2,    // CPMM中等
            Dex::MeteoraAmm => 2,     // AMM中等
            Dex::Orca => 2,           // Whirlpool中等
            Dex::PumpSwap => 1,       // PumpSwap较简单
        }
    }).sum();

    base_score + diversity_score + dex_complexity
}

/// 验证账户签名和权限
pub fn validate_account_authority(
    authority: &AccountInfo,
    expected_authority: &Pubkey,
    requires_signer: bool,
    allow_pda: bool,
) -> Result<()> {
    // 验证账户地址
    if authority.key() != *expected_authority {
        msg!(
            "权限账户不匹配：期望 {}, 实际 {}",
            expected_authority,
            authority.key()
        );
        return Err(RouteError::PermissionDenied.into());
    }

    // 验证签名或PDA
    if requires_signer {
        if !authority.is_signer {
            if !allow_pda {
                msg!("权限账户未签名：{}", authority.key());
                return Err(RouteError::PermissionDenied.into());
            }
            // 如果允许PDA，则检查是否为PDA账户
            if authority.owner != &crate::ID {
                msg!("PDA账户所有者无效：{}", authority.owner);
                return Err(RouteError::PermissionDenied.into());
            }
        }
    }

    Ok(())
}

/// 验证PDA推导是否正确
pub fn validate_pda(
    pda_account: &AccountInfo,
    seeds: &[&[u8]],
    program_id: &Pubkey,
) -> Result<u8> {
    let (expected_pda, bump) = Pubkey::find_program_address(seeds, program_id);

    if *pda_account.key != expected_pda {
        msg!("PDA地址不匹配: 期望 {}, 实际 {}", expected_pda, pda_account.key());
        return Err(RouteError::InvalidDexAccounts.into());
    }

    if pda_account.owner != program_id {
        msg!("PDA所有者无效: 期望 {}, 实际 {}", program_id, pda_account.owner);
        return Err(RouteError::InvalidDexAccounts.into());
    }

    Ok(bump)
}

/// 验证价格影响在可接受范围内
pub fn validate_price_impact(
    _input_amount: u64,
    output_amount: u64,
    expected_output: u64,
    max_impact_bps: u16,
) -> Result<()> {
    // 计算价格影响
    let impact_bps = if output_amount < expected_output {
        let diff = expected_output - output_amount;
        (diff * 10000) / expected_output
    } else {
        0 // 正价格影响（更好的价格）
    };

    if impact_bps > max_impact_bps as u64 {
        msg!(
            "价格影响过大：{}基点 > {}基点",
            impact_bps,
            max_impact_bps
        );
        return Err(RouteError::SlippageTooHigh.into());
    }

    Ok(())
}

/// 验证时间窗口
pub fn validate_time_window(
    current_time: i64,
    start_time: i64,
    max_duration: i64,
) -> Result<()> {
    let elapsed = current_time - start_time;

    if elapsed > max_duration {
        msg!(
            "操作超时：已过{}秒，最大允许{}秒",
            elapsed,
            max_duration
        );
        return Err(RouteError::OperationTimeout.into());
    }

    Ok(())
}

/// 批量验证路由配置
pub fn validate_route_batch(
    route_configs: &[RouteConfig],
    max_batch_size: usize,
) -> Result<()> {
    if route_configs.len() > max_batch_size {
        msg!(
            "批量大小过大：{} > {}",
            route_configs.len(),
            max_batch_size
        );
        return Err(RouteError::RoutePathTooLong.into());
    }

    // 验证每个路由配置
    for (i, config) in route_configs.iter().enumerate() {
        config.validate().map_err(|e| {
            msg!("批量路由第{}个配置验证失败", i);
            e
        })?;
    }

    Ok(())
}

/// 路由特定安全验证模块
/// 仅为路由特定功能添加验证，避免重复DEX Router已有功能
pub mod route_security {
    use super::*;

    /// 循环路由验证（套利模式）
    /// 增强版本，支持更严格的循环检查
    pub fn validate_circular_route(routes: &[Route]) -> Result<()> {
        // 基础检查：路由数量
        if routes.len() < 2 {
            msg!("循环路由至少需要2个步骤，当前: {}", routes.len());
            return Err(RouteError::InvalidRouteConfig.into());
        }

        if routes.len() > 6 {
            msg!("循环路由最多支持6个步骤，当前: {}", routes.len());
            return Err(RouteError::RoutePathTooLong.into());
        }

        // 验证路径连续性
        validate_route_continuity(routes)?;

        // 验证循环性：起始和结束代币必须相同
        let start_token = routes.first().unwrap().input_mint;
        let end_token = routes.last().unwrap().output_mint;

        if start_token != end_token {
            msg!(
                "循环路由验证失败：起始代币 {} != 结束代币 {}",
                start_token,
                end_token
            );
            return Err(RouteError::NotCircularRoute.into());
        }

        // 额外检查：确保中间不会回到起始代币（避免过早循环）
        for (i, route) in routes.iter().enumerate() {
            if i > 0 && i < routes.len() - 1 && route.output_mint == start_token {
                msg!("循环路由在第{}步过早回到起始代币", i);
                return Err(RouteError::InvalidRouteConfig.into());
            }
        }

        Ok(())
    }

    /// 路由路径连续性验证（增强版）
    /// 添加更详细的错误信息和额外检查
    pub fn validate_route_continuity_enhanced(routes: &[Route]) -> Result<()> {
        if routes.is_empty() {
            return Err(RouteError::EmptyRoutePath.into());
        }

        for i in 0..routes.len().saturating_sub(1) {
            let current_route = &routes[i];
            let next_route = &routes[i + 1];

            if current_route.output_mint != next_route.input_mint {
                msg!(
                    "路由不连续：步骤{} [{}->{}] 与 步骤{} [{}->{}] 不匹配",
                    i,
                    current_route.input_mint,
                    current_route.output_mint,
                    i + 1,
                    next_route.input_mint,
                    next_route.output_mint
                );
                return Err(RouteError::RouteDiscontinuity.into());
            }

            // 检查是否有相同的输入输出代币（无意义的交易）
            if current_route.input_mint == current_route.output_mint {
                msg!("步骤{}存在相同的输入输出代币: {}", i, current_route.input_mint);
                return Err(RouteError::InvalidRouteConfig.into());
            }
        }

        Ok(())
    }

    /// 验证路由DEX多样性（避免重复使用同一个DEX池）
    pub fn validate_route_diversity(routes: &[Route]) -> Result<()> {
        let mut dex_usage = HashMap::new();

        for (i, route) in routes.iter().enumerate() {
            let dex_key = (route.dex, route.input_mint, route.output_mint);

            if let Some(&first_usage) = dex_usage.get(&dex_key) {
                msg!(
                    "警告：步骤{}与步骤{}使用了相同的DEX池 {:?} {}->{}",
                    first_usage, i, route.dex, route.input_mint, route.output_mint
                );
                // 暂时只记录警告，不阻止执行
            } else {
                dex_usage.insert(dex_key, i);
            }
        }

        Ok(())
    }

    /// 验证路由金额合理性
    pub fn validate_route_amounts(routes: &[Route], total_amount_in: u64) -> Result<()> {
        for (i, route) in routes.iter().enumerate() {
            // 检查最小输出金额是否合理
            if route.min_amount_out == 0 {
                msg!("步骤{}的最小输出金额不能为0", i);
                return Err(RouteError::AmountValidationFailed.into());
            }

            // 对于第一步，检查金额是否与输入匹配
            if i == 0 && total_amount_in == 0 {
                msg!("第一步的输入金额不能为0");
                return Err(RouteError::AmountValidationFailed.into());
            }
        }

        Ok(())
    }
}

/// 多步路由额外安全检查
/// 参考DEX Router的重入攻击保护，添加路由特定检查
pub fn check_multi_hop_security(
    route_config: &RouteConfig,
) -> Result<()> {
    // 使用DEX Router的现有安全检查（调用现有函数）
    for route in route_config.routes.iter() {
        // 这里可以调用现有的单个交易验证函数
        validate_supported_dex(&route.dex, &[
            Dex::RaydiumClmm,
            Dex::RaydiumCpmm,
            Dex::MeteoraAmm,
            Dex::MeteoraLb,
            Dex::Orca,
            Dex::PumpSwap,
        ])?;
    }

    // 仅添加路由特定检查
    match route_config.mode {
        RoutingMode::Circular => {
            route_security::validate_circular_route(&route_config.routes)?;
        },
        RoutingMode::Linear => {
            route_security::validate_route_continuity_enhanced(&route_config.routes)?;
        },
        RoutingMode::Branching | RoutingMode::Batched => {
            // 分支和批量路由有单独的验证逻辑
            route_security::validate_route_continuity_enhanced(&route_config.routes)?;
        }
    }

    // 验证路由多样性和金额合理性
    route_security::validate_route_diversity(&route_config.routes)?;
    route_security::validate_route_amounts(&route_config.routes, route_config.amount_in)?;

    Ok(())
}

/// 统一的金额限制检查
pub fn validate_arbitrage_amounts(
    config: &RouterConfig,
    amount_in: u64,
    expected_profit: u64,
) -> Result<()> {
    // 检查路由金额限制
    if amount_in > config.max_route_amount {
        msg!("交易金额超限: {} > {}", amount_in, config.max_route_amount);
        return Err(RouteError::AmountValidationFailed.into());
    }

    if amount_in < config.min_route_amount {
        msg!("交易金额过小: {} < {}", amount_in, config.min_route_amount);
        return Err(RouteError::AmountValidationFailed.into());
    }

    // 检查利润阈值
    if expected_profit < config.min_profit_threshold {
        msg!("预期利润过低: {} < {}", expected_profit, config.min_profit_threshold);
        return Err(RouteError::ProfitCalculationError.into());
    }

    Ok(())
}

/// 分级紧急控制机制（全局 + 单DEX + 单用户）
pub fn check_emergency_controls(
    config: &RouterConfig,
    user_position: &UserPosition,
    routes: &[Route],
) -> Result<()> {
    // 全局紧急停止
    if config.emergency_stop {
        msg!("全局紧急停止已启动");
        return Err(RouteError::GlobalEmergencyStop.into());
    }

    // 用户级别停止
    if user_position.is_suspended {
        msg!("用户已被暂停: {}", user_position.user);
        return Err(RouteError::UserSuspended.into());
    }

    // 检查用户风险等级限制
    if user_position.risk_level >= 5 {
        msg!("用户风险等级过高: {} (最高风险)", user_position.risk_level);
        return Err(RouteError::RiskScoreTooHigh.into());
    }

    // 单DEX停止检查
    for route in routes {
        if config.dex_emergency_stops.contains(&route.dex) {
            msg!("DEX紧急停止已启动: {:?}", route.dex);
            return Err(RouteError::DexEmergencyStop.into());
        }
    }

    Ok(())
}

/// 全面的路由安全检查（整合新的安全验证功能）
pub fn comprehensive_route_security_check(
    config: &Account<RouterConfig>,
    user_position: &Account<UserPosition>,
    route_config: &RouteConfig,
    user: &AccountInfo,
) -> Result<()> {
    // 1. 分级紧急控制检查（全局 + DEX + 用户）
    check_emergency_controls(config, user_position, &route_config.routes)?;

    // 2. 多步路由安全检查
    check_multi_hop_security(route_config)?;

    // 3. 检查用户日常限额
    let _current_slot = Clock::get()?.slot;
    if user_position.daily_volume_used > config.max_daily_volume_per_user {
        msg!("用户日交易量超限: {} > {}",
            user_position.daily_volume_used, config.max_daily_volume_per_user);
        return Err(RouteError::AmountValidationFailed.into());
    }

    // 4. 金额范围检查
    validate_amount_range(
        route_config.amount_in,
        config.min_route_amount,
        config.max_route_amount,
    )?;

    // 5. 滑点检查
    if route_config.max_slippage_bps > config.max_slippage_bps {
        msg!("滑点设置过高: {} > {}",
            route_config.max_slippage_bps, config.max_slippage_bps);
        return Err(RouteError::SlippageTooHigh.into());
    }

    // 6. 路由复杂度检查
    validate_route_complexity(
        &route_config.routes,
        config.max_route_steps as usize,
        config.max_complexity_score,
    )?;

    // 7. 时间窗口检查（防止过期指令）
    let current_time = Clock::get()?.unix_timestamp;
    if let Some(deadline) = user_position.last_activity_deadline {
        validate_time_window(current_time, user_position.last_activity_time, deadline)?;
    }

    // 8. 用户权限验证
    validate_account_authority(
        user,
        &user_position.user,
        true,  // 需要签名
        false, // 不允许PDA
    )?;

    msg!("路由安全检查通过 - 用户: {}, 模式: {:?}, 步骤: {}",
        user.key(), route_config.mode, route_config.routes.len());

    Ok(())
}

/// 验证闪电贷安全性（整合新的安全验证）
pub fn validate_flash_loan_security(
    config: &Account<RouterConfig>,
    user_position: &Account<UserPosition>,
    flash_amount: u64,
    expected_profit: u64,
    arbitrage_routes: &[Route],
) -> Result<()> {
    // 1. 基础紧急控制检查
    check_emergency_controls(config, user_position, arbitrage_routes)?;

    // 2. 闪电贷特定的金额检查
    validate_arbitrage_amounts(config, flash_amount, expected_profit)?;

    // 3. 闪电贷金额限制
    if flash_amount > config.max_flash_loan_amount {
        msg!("闪电贷金额超限: {} > {}", flash_amount, config.max_flash_loan_amount);
        return Err(RouteError::FlashLoanAmountExceeded.into());
    }

    // 4. 验证套利路由的循环性
    route_security::validate_circular_route(arbitrage_routes)?;

    // 5. 风险评分检查
    let risk_score = calculate_flash_loan_risk_score(
        flash_amount,
        expected_profit,
        user_position.total_successful_trades,
        user_position.total_failed_trades,
    );

    if risk_score > config.max_risk_score {
        msg!("闪电贷风险评分过高: {} > {}", risk_score, config.max_risk_score);
        return Err(RouteError::RiskScoreTooHigh.into());
    }

    // 6. 用户历史表现检查
    let success_rate = user_position.success_rate();
    if success_rate < 70 && user_position.risk_level > 3 {
        msg!("用户成功率过低且风险等级过高: {}%, 等级: {}",
            success_rate, user_position.risk_level);
        return Err(RouteError::RiskScoreTooHigh.into());
    }

    msg!("闪电贷安全检查通过 - 金额: {}, 预期利润: {}, 风险评分: {}",
        flash_amount, expected_profit, risk_score);

    Ok(())
}

/// 计算闪电贷风险评分
pub fn calculate_flash_loan_risk_score(
    flash_amount: u64,
    expected_profit: u64,
    successful_trades: u64,
    failed_trades: u64,
) -> u8 {
    let mut score = 0u8;

    // 金额风险（30%权重）
    if flash_amount > 1_000_000_000 { // > 1000 USDC
        score += 30;
    } else if flash_amount > 100_000_000 { // > 100 USDC
        score += 20;
    } else {
        score += 10;
    }

    // 利润率风险（25%权重）
    let profit_rate_bps = if flash_amount > 0 {
        (expected_profit * 10000) / flash_amount
    } else {
        0
    };

    if profit_rate_bps < 50 { // < 0.5%
        score += 25;
    } else if profit_rate_bps < 100 { // < 1%
        score += 15;
    } else {
        score += 5;
    }

    // 历史表现风险（25%权重）
    let total_trades = successful_trades + failed_trades;
    if total_trades == 0 {
        score += 25; // 新用户高风险
    } else {
        let success_rate = (successful_trades * 100) / total_trades;
        if success_rate < 80 {
            score += 20;
        } else if success_rate < 90 {
            score += 10;
        } else {
            score += 5;
        }
    }

    // 交易频率风险（20%权重）
    if total_trades > 1000 {
        score += 5; // 高频交易者风险较低
    } else if total_trades > 100 {
        score += 10;
    } else {
        score += 20;
    }

    score.min(100)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_route_complexity_calculation() {
        let routes = vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: Pubkey::default(),
                output_mint: Pubkey::default(),
                swap_data: vec![1, 2, 3],
                min_amount_out: 1000,
            },
            Route {
                dex: Dex::MeteoraLb,
                input_mint: Pubkey::default(),
                output_mint: Pubkey::default(),
                swap_data: vec![4, 5, 6],
                min_amount_out: 2000,
            },
        ];

        let complexity = calculate_route_complexity(&routes);
        assert!(complexity > 0);
        // 基础分数2 + 多样性分数2 + DEX复杂度分数6 = 10
        assert_eq!(complexity, 10);
    }

    #[test]
    fn test_flash_loan_risk_calculation() {
        // 测试新用户高金额的风险评分
        let score = calculate_flash_loan_risk_score(
            2_000_000_000, // 2000 USDC
            5_000_000,     // 5 USDC profit (0.25%)
            0,             // 无成功交易
            0,             // 无失败交易
        );

        // 应该是高风险评分
        assert!(score > 80);

        // 测试经验丰富用户的风险评分
        let score2 = calculate_flash_loan_risk_score(
            100_000_000,   // 100 USDC
            2_000_000,     // 2 USDC profit (2%)
            950,           // 950次成功
            50,            // 50次失败
        );

        // 应该是低风险评分
        assert!(score2 < 40);
    }

    #[test]
    fn test_circular_route_validation() {
        let mut routes = vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![1],
                min_amount_out: 100,
            },
            Route {
                dex: Dex::MeteoraLb,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![2],
                min_amount_out: 200,
            },
        ];

        // 非循环路由应该失败
        assert!(validate_circular_route(&routes).is_err());

        // 修正为循环路由
        routes[1].input_mint = routes[0].output_mint;
        routes[1].output_mint = routes[0].input_mint;

        // 循环路由应该成功
        assert!(validate_circular_route(&routes).is_ok());
    }

    #[test]
    fn test_amount_validation() {
        assert!(validate_amount_range(100, 50, 200).is_ok());
        assert!(validate_amount_range(10, 50, 200).is_err());
        assert!(validate_amount_range(300, 50, 200).is_err());
    }

    #[test]
    fn test_slippage_validation() {
        assert!(validate_slippage(100, 500).is_ok());
        assert!(validate_slippage(600, 500).is_err());
    }

    #[test]
    fn test_price_impact_validation() {
        assert!(validate_price_impact(
            1000, 950, 1000, 500 // 5%影响，允许5%
        ).is_ok());

        assert!(validate_price_impact(
            1000, 900, 1000, 500 // 10%影响，只允许5%
        ).is_err());
    }

    #[test]
    fn test_route_security_circular_validation() {
        let mint_a = Pubkey::new_unique();
        let mint_b = Pubkey::new_unique();
        let mint_c = Pubkey::new_unique();

        // 测试有效的循环路由
        let valid_circular_routes = vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: mint_a,
                output_mint: mint_b,
                swap_data: vec![1],
                min_amount_out: 100,
            },
            Route {
                dex: Dex::MeteoraLb,
                input_mint: mint_b,
                output_mint: mint_c,
                swap_data: vec![2],
                min_amount_out: 200,
            },
            Route {
                dex: Dex::Orca,
                input_mint: mint_c,
                output_mint: mint_a, // 回到起始代币
                swap_data: vec![3],
                min_amount_out: 300,
            },
        ];

        assert!(route_security::validate_circular_route(&valid_circular_routes).is_ok());

        // 测试无效的循环路由（非循环）
        let invalid_routes = vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: mint_a,
                output_mint: mint_b,
                swap_data: vec![1],
                min_amount_out: 100,
            },
            Route {
                dex: Dex::MeteoraLb,
                input_mint: mint_b,
                output_mint: mint_c, // 没有回到起始代币
                swap_data: vec![2],
                min_amount_out: 200,
            },
        ];

        assert!(route_security::validate_circular_route(&invalid_routes).is_err());
    }

    #[test]
    fn test_route_security_continuity_enhanced() {
        let mint_a = Pubkey::new_unique();
        let mint_b = Pubkey::new_unique();
        let mint_c = Pubkey::new_unique();

        // 测试连续的路由
        let continuous_routes = vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: mint_a,
                output_mint: mint_b,
                swap_data: vec![1],
                min_amount_out: 100,
            },
            Route {
                dex: Dex::MeteoraLb,
                input_mint: mint_b,
                output_mint: mint_c,
                swap_data: vec![2],
                min_amount_out: 200,
            },
        ];

        assert!(route_security::validate_route_continuity_enhanced(&continuous_routes).is_ok());

        // 测试不连续的路由
        let discontinuous_routes = vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: mint_a,
                output_mint: mint_b,
                swap_data: vec![1],
                min_amount_out: 100,
            },
            Route {
                dex: Dex::MeteoraLb,
                input_mint: mint_c, // 不匹配前一个输出
                output_mint: mint_b,
                swap_data: vec![2],
                min_amount_out: 200,
            },
        ];

        assert!(route_security::validate_route_continuity_enhanced(&discontinuous_routes).is_err());

        // 测试相同输入输出代币的无效路由
        let same_mint_route = vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: mint_a,
                output_mint: mint_a, // 相同的输入输出
                swap_data: vec![1],
                min_amount_out: 100,
            },
        ];

        assert!(route_security::validate_route_continuity_enhanced(&same_mint_route).is_err());
    }

    #[test]
    fn test_validate_arbitrage_amounts() {
        let config = RouterConfig {
            admin: Pubkey::default(),
            supported_dexes: vec![],
            max_route_amount: 1000000,
            max_flash_loan_amount: 500000,
            max_slippage_bps: 500,
            min_profit_threshold: 1000,
            max_gas_fee: 50000,
            protocol_fee_bps: 30,
            min_route_amount: 100,
            max_route_steps: 6,
            max_complexity_score: 20,
            max_risk_score: 80,
            max_daily_volume_per_user: 10000000,
            emergency_stop: false,
            dex_emergency_stops: vec![],
            created_at: 0,
            updated_at: 0,
            reserved: [0; 8],
        };

        // 测试有效金额
        assert!(validate_arbitrage_amounts(&config, 50000, 2000).is_ok());

        // 测试金额过大
        assert!(validate_arbitrage_amounts(&config, 2000000, 2000).is_err());

        // 测试金额过小
        assert!(validate_arbitrage_amounts(&config, 50, 2000).is_err());

        // 测试利润过低
        assert!(validate_arbitrage_amounts(&config, 50000, 500).is_err());
    }

    #[test]
    fn test_check_emergency_controls() {
        let config = RouterConfig {
            admin: Pubkey::default(),
            supported_dexes: vec![Dex::RaydiumClmm, Dex::MeteoraLb],
            max_route_amount: 1000000,
            max_flash_loan_amount: 500000,
            max_slippage_bps: 500,
            min_profit_threshold: 1000,
            max_gas_fee: 50000,
            protocol_fee_bps: 30,
            min_route_amount: 100,
            max_route_steps: 6,
            max_complexity_score: 20,
            max_risk_score: 80,
            max_daily_volume_per_user: 10000000,
            emergency_stop: false,
            dex_emergency_stops: vec![Dex::PumpSwap],
            created_at: 0,
            updated_at: 0,
            reserved: [0; 8],
        };

        let normal_user = UserPosition {
            user: Pubkey::default(),
            is_suspended: false,
            risk_level: 2,
            total_volume: 0,
            total_profit: 0,
            total_loss: 0,
            successful_routes: 0,
            failed_routes: 0,
            total_successful_trades: 0,
            total_failed_trades: 0,
            daily_volume_used: 0,
            last_activity_time: 0,
            last_activity_deadline: None,
            created_at: 0,
            last_activity: 0,
            reserved: [0; 8],
        };

        let routes = vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![1],
                min_amount_out: 100,
            },
        ];

        // 测试正常情况
        assert!(check_emergency_controls(&config, &normal_user, &routes).is_ok());

        // 测试全局紧急停止
        let mut emergency_config = config.clone();
        emergency_config.emergency_stop = true;
        assert!(check_emergency_controls(&emergency_config, &normal_user, &routes).is_err());

        // 测试用户被暂停
        let mut suspended_user = normal_user.clone();
        suspended_user.is_suspended = true;
        assert!(check_emergency_controls(&config, &suspended_user, &routes).is_err());

        // 测试高风险用户
        let mut high_risk_user = normal_user.clone();
        high_risk_user.risk_level = 5;
        assert!(check_emergency_controls(&config, &high_risk_user, &routes).is_err());

        // 测试DEX紧急停止
        let blocked_routes = vec![
            Route {
                dex: Dex::PumpSwap, // 在紧急停止列表中
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![1],
                min_amount_out: 100,
            },
        ];
        assert!(check_emergency_controls(&config, &normal_user, &blocked_routes).is_err());
    }
}
