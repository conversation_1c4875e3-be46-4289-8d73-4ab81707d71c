//! PumpSwap DEX适配器
//!
//! 支持Pump.fun协议的完整交换功能
//! 包含bonding curve计算、创建者费用处理、价格发现和真实CPI调用

use anchor_lang::prelude::*;
use anchor_spl::token::TokenAccount;
use crate::adapters::common::{DexProcessor, SwapAccountIndices};
use crate::routing::{Route, Dex};
use crate::error::RouteError;


use std::str::FromStr;
use anchor_lang::solana_program::instruction::Instruction;
use anchor_lang::solana_program::program::{invoke, invoke_signed};

/// PumpSwap程序常量
pub mod constants {

    pub const PUMP_PROGRAM_ID: &str = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
    pub const PUMP_FEE_RECIPIENT: &str = "CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM";
    pub const GLOBAL_ACCOUNT: &str = "4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf";
    pub const SYSTEM_PROGRAM_ID: &str = "11111111111111111111111111111111";
    pub const RENT_PROGRAM_ID: &str = "SysvarRent111111111111111111111111111111111";
    pub const ASSOCIATED_TOKEN_PROGRAM_ID: &str = "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL";

    // 费率配置
    pub const CREATOR_FEE_BPS: u16 = 100; // 1%
    pub const PLATFORM_FEE_BPS: u16 = 100; // 1%
}

/// PumpSwap交换账户结构
#[derive(Debug, Clone)]
pub struct SwapAccounts {
    pub global: Pubkey,
    pub fee_recipient: Pubkey,
    pub mint: Pubkey,
    pub bonding_curve: Pubkey,
    pub associated_bonding_curve: Pubkey,
    pub associated_user: Pubkey,
    pub user: Pubkey,
    pub system_program: Pubkey,
    pub token_program: Pubkey,
    pub rent: Pubkey,
    pub event_authority: Pubkey,
    pub program: Pubkey,
}

/// PumpSwap交换指令数据
#[derive(Debug, Clone, AnchorSerialize, AnchorDeserialize)]
pub struct SwapInstructionData {
    pub amount: u64,
    pub max_sol_cost: u64,
}

/// PumpSwap指令构建器
pub struct PumpSwapInstructionBuilder;

impl PumpSwapInstructionBuilder {
    pub fn create_buy_instruction(
        accounts: &SwapAccounts,
        amount: u64,
        max_sol_cost: u64,
    ) -> Result<Instruction> {
        let program_id = Pubkey::from_str(constants::PUMP_PROGRAM_ID)
            .map_err(|_| RouteError::InvalidProgram)?;

        let instruction_data = SwapInstructionData {
            amount,
            max_sol_cost,
        };

        let data = instruction_data.try_to_vec()
            .map_err(|_| RouteError::InvalidRouteConfig)?;

        Ok(Instruction {
            program_id,
            accounts: vec![], // 应该包含实际的账户元数据
            data,
        })
    }

    pub fn create_sell_instruction(
        accounts: &SwapAccounts,
        amount: u64,
        min_sol_output: u64,
    ) -> Result<Instruction> {
        let program_id = Pubkey::from_str(constants::PUMP_PROGRAM_ID)
            .map_err(|_| RouteError::InvalidProgram)?;

        let instruction_data = SwapInstructionData {
            amount,
            max_sol_cost: min_sol_output, // 重用字段
        };

        let data = instruction_data.try_to_vec()
            .map_err(|_| RouteError::InvalidRouteConfig)?;

        Ok(Instruction {
            program_id,
            accounts: vec![], // 应该包含实际的账户元数据
            data,
        })
    }
}

/// 便捷函数：创建买入指令
pub fn create_buy_instruction(
    accounts: &SwapAccounts,
    amount: u64,
    max_sol_cost: u64,
) -> Result<Instruction> {
    PumpSwapInstructionBuilder::create_buy_instruction(accounts, amount, max_sol_cost)
}

/// 便捷函数：创建卖出指令
pub fn create_sell_instruction(
    accounts: &SwapAccounts,
    amount: u64,
    min_sol_output: u64,
) -> Result<Instruction> {
    PumpSwapInstructionBuilder::create_sell_instruction(accounts, amount, min_sol_output)
}

/// PumpSwap适配器
///
/// 实现Pump.fun协议的交换逻辑
/// 支持bonding curve定价、创建者费用分成和流动性挖矿
pub struct PumpSwapProcessor {
    /// 最大滑点容忍度（基点）
    pub max_slippage_bps: u16,
    /// 价格影响阈值（基点）
    pub max_price_impact_bps: u16,
    /// 创建者费用比例（基点）
    pub creator_fee_bps: u16,
}

impl Default for PumpSwapProcessor {
    fn default() -> Self {
        Self {
            max_slippage_bps: 200, // 2%
            max_price_impact_bps: 1000, // 10%
            creator_fee_bps: 100, // 1%给创建者
        }
    }
}

/// PumpSwap交换参数
#[derive(Debug, Clone, AnchorSerialize, AnchorDeserialize)]
pub struct PumpSwapParams {
    pub pool: Pubkey,
    pub token_mint: Pubkey,
    pub creator: Pubkey,
    pub is_buy: bool,
    pub virtual_sol_reserves: u64,
    pub virtual_token_reserves: u64,
    pub real_sol_reserves: u64,
    pub real_token_reserves: u64,
    pub token_total_supply: u64,
    pub complete_marker_cap: u64,
}

impl DexProcessor for PumpSwapProcessor {
    fn execute_swap_cpi<'info>(
        &self,
        route: &Route,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        min_amount_out: u64,
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<u64> {
        msg!("执行PumpSwap交换: {} -> {}, 最小输出: {}", amount_in, min_amount_out, min_amount_out);

        // 解析PumpSwap交换参数
        let swap_params = self.parse_pump_swap_params(&route.swap_data)?;

        // 使用bonding curve计算预期输出
        let estimated_output = if swap_params.is_buy {
            self.calculate_buy_output(&swap_params, amount_in)?
        } else {
            self.calculate_sell_output(&swap_params, amount_in)?
        };

        // 验证滑点
        if estimated_output < min_amount_out {
            return Err(RouteError::SlippageTooHigh.into());
        }

        // 验证价格影响
        self.validate_price_impact(&swap_params, amount_in, estimated_output)?;

        // 构建交换指令
        let user = accounts[1].key(); // 用户在第1个位置
        let swap_instruction = if swap_params.is_buy {
            create_buy_instruction(
                user,
                swap_params.token_mint,
                swap_params.pool,
                amount_in,
                min_amount_out,
                self.max_slippage_bps as u64,
                swap_params.creator,
            )
        } else {
            create_sell_instruction(
                user,
                swap_params.token_mint,
                swap_params.pool,
                amount_in,
                min_amount_out,
                self.max_slippage_bps as u64,
                swap_params.creator,
            )
        }.map_err(|e| {
            msg!("构建PumpSwap指令失败: {:?}", e);
            RouteError::DexOperationFailed
        })?;

        // 执行CPI调用
        self.execute_pump_cpi(&swap_instruction, accounts, owner_seeds)?;

        Ok(estimated_output)
    }

    fn validate_accounts(
        &self,
        route: &Route,
        accounts: &[AccountInfo],
        hop_index: usize,
    ) -> Result<()> {
        // 验证账户数量（19个账户）
        if accounts.len() < 19 {
            msg!("PumpSwap需要19个账户，实际: {}", accounts.len());
            return Err(RouteError::InvalidAccountCount.into());
        }

        // 解析交换参数
        let swap_params = self.parse_pump_swap_params(&route.swap_data)?;

        // 验证关键账户
        let indices = self.get_account_indices(accounts)?;

        // 验证用户签名权限
        if hop_index == 0 && !accounts[indices.swap_authority].is_signer {
            return Err(RouteError::SwapAuthorityIsNotSigner.into());
        }

        // 验证源代币账户
        let source_token_account = &accounts[indices.source_token_account];
        let source_token_data = TokenAccount::try_deserialize(
            &mut source_token_account.data.borrow().as_ref()
        ).map_err(|_| RouteError::InvalidDexAccounts)?;

        if source_token_data.mint != route.input_mint {
            msg!("源代币mint不匹配: 期望 {}, 实际 {}", route.input_mint, source_token_data.mint);
            return Err(RouteError::TokenAccountMintMismatch.into());
        }

        // 验证目标代币账户
        let dest_token_account = &accounts[indices.destination_token_account];
        let dest_token_data = TokenAccount::try_deserialize(
            &mut dest_token_account.data.borrow().as_ref()
        ).map_err(|_| RouteError::InvalidDexAccounts)?;

        if dest_token_data.mint != route.output_mint {
            msg!("目标代币mint不匹配: 期望 {}, 实际 {}", route.output_mint, dest_token_data.mint);
            return Err(RouteError::TokenAccountMintMismatch.into());
        }

        // 验证池账户
        if let Some(pool_idx) = indices.pool_account {
            if *accounts[pool_idx].key != swap_params.pool {
                msg!("池账户不匹配: 期望 {}, 实际 {}", swap_params.pool, accounts[pool_idx].key);
                return Err(RouteError::InvalidDexAccounts.into());
            }
        }

        msg!("PumpSwap账户验证通过: {} -> {}", route.input_mint, route.output_mint);
        Ok(())
    }

    fn get_account_indices(
        &self,
        accounts: &[AccountInfo],
    ) -> Result<SwapAccountIndices> {
        // PumpSwap的标准账户布局
        // 0: pool
        // 1: user (signer)
        // 2: global_config
        // 3: token_mint
        // 4: quote_mint (WSOL)
        // 5: user_token_account
        // 6: user_quote_account
        // 7: pool_token_account
        // 8: pool_quote_account
        // 9: fee_recipient
        // 10: fee_recipient_account
        // 11: token_program
        // 12: quote_program
        // 13: system_program
        // 14: associated_token_program
        // 15: event_authority
        // 16: program_id
        // 17: coin_creator_vault_ata
        // 18: coin_creator_vault_authority

        if accounts.len() < 19 {
            return Err(RouteError::InvalidAccountCount.into());
        }

        Ok(SwapAccountIndices {
            source_token_account: 5,
            destination_token_account: 6,
            swap_authority: 1,
            dex_program: 16,
            pool_account: Some(0),
            additional_accounts: vec![2, 3, 4, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18],
        })
    }

    fn dex_name(&self) -> &'static str {
        "PumpSwap"
    }

    fn dex_type(&self) -> Dex {
        Dex::PumpSwap
    }

    fn estimate_gas_cost(&self, _route: &Route) -> u64 {
        // PumpSwap需要创建关联代币账户等，Gas消耗较高
        180_000
    }
}

impl PumpSwapProcessor {
    /// 解析PumpSwap交换参数
    fn parse_pump_swap_params(&self, swap_data: &[u8]) -> Result<PumpSwapParams> {
        if swap_data.is_empty() {
            return Err(RouteError::InvalidRouteConfig.into());
        }

        PumpSwapParams::try_from_slice(swap_data)
            .map_err(|e| {
                msg!("解析PumpSwap交换参数失败: {:?}", e);
                RouteError::InvalidRouteConfig.into()
            })
    }

    /// 计算买入输出（SOL -> Token）
    fn calculate_buy_output(&self, params: &PumpSwapParams, sol_amount_in: u64) -> Result<u64> {
        // Pump.fun使用bonding curve定价
        // 公式: token_out = virtual_token_reserves * sol_in / (virtual_sol_reserves + sol_in)

        let sol_in = sol_amount_in as u128;
        let virtual_sol = params.virtual_sol_reserves as u128;
        let virtual_token = params.virtual_token_reserves as u128;

        // 检查是否会导致除零
        if virtual_sol == 0 {
            return Err(RouteError::DivisionByZero.into());
        }

        // 计算不含费用的输出
        let token_out_before_fees = virtual_token
            .checked_mul(sol_in)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(virtual_sol.checked_add(sol_in).ok_or(RouteError::MathOverflow)?)
            .ok_or(RouteError::DivisionByZero.into())?;

        // 扣除创建者费用
        let creator_fee = self.calculate_creator_fee(token_out_before_fees as u64)?;
        let token_out = (token_out_before_fees as u64)
            .checked_sub(creator_fee)
            .ok_or(RouteError::MathUnderflow.into())?;

        msg!("PumpSwap买入计算: SOL输入 {} -> Token输出 {}, 创建者费用 {}",
            sol_amount_in, token_out, creator_fee);

        Ok(token_out)
    }

    /// 计算卖出输出（Token -> SOL）
    fn calculate_sell_output(&self, params: &PumpSwapParams, token_amount_in: u64) -> Result<u64> {
        // Pump.fun卖出公式: sol_out = virtual_sol_reserves * token_in / (virtual_token_reserves + token_in)

        let token_in = token_amount_in as u128;
        let virtual_sol = params.virtual_sol_reserves as u128;
        let virtual_token = params.virtual_token_reserves as u128;

        // 检查是否会导致除零
        if virtual_token == 0 {
            return Err(RouteError::DivisionByZero.into());
        }

        // 计算不含费用的输出
        let sol_out_before_fees = virtual_sol
            .checked_mul(token_in)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(virtual_token.checked_add(token_in).ok_or(RouteError::MathOverflow)?)
            .ok_or(RouteError::DivisionByZero.into())?;

        // 扣除创建者费用
        let creator_fee = self.calculate_creator_fee(sol_out_before_fees as u64)?;
        let sol_out = (sol_out_before_fees as u64)
            .checked_sub(creator_fee)
            .ok_or(RouteError::MathUnderflow.into())?;

        msg!("PumpSwap卖出计算: Token输入 {} -> SOL输出 {}, 创建者费用 {}",
            token_amount_in, sol_out, creator_fee);

        Ok(sol_out)
    }

    /// 计算创建者费用
    fn calculate_creator_fee(&self, amount: u64) -> Result<u64> {
        amount
            .checked_mul(self.creator_fee_bps as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::DivisionByZero.into())
    }

    /// 验证价格影响
    fn validate_price_impact(
        &self,
        params: &PumpSwapParams,
        amount_in: u64,
        _estimated_out: u64,
    ) -> Result<()> {
        if amount_in == 0 {
            return Ok(());
        }

        // 计算价格影响百分比
        // 对于bonding curve，价格影响 = amount_in / (virtual_reserves + amount_in)
        let virtual_reserves = if params.is_buy {
            params.virtual_sol_reserves
        } else {
            params.virtual_token_reserves
        };

        let price_impact = amount_in
            .checked_mul(10000)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(virtual_reserves.checked_add(amount_in).ok_or(RouteError::MathOverflow)?)
            .ok_or(RouteError::DivisionByZero.into())?;

        if price_impact as u16 > self.max_price_impact_bps {
            msg!("价格影响过高: {}基点 > {}基点", price_impact, self.max_price_impact_bps);
            return Err(RouteError::PriceImpactTooHigh.into());
        }

        Ok(())
    }

    /// 检查是否可以完成bonding curve
    fn can_complete_bonding_curve(&self, params: &PumpSwapParams, amount_in: u64) -> Result<bool> {
        if !params.is_buy {
            return Ok(false); // 只有买入才可能完成bonding curve
        }

        let new_sol_reserves = params.real_sol_reserves
            .checked_add(amount_in)
            .ok_or(RouteError::MathOverflow)?;

        Ok(new_sol_reserves >= params.complete_marker_cap)
    }

    /// 执行PumpSwap CPI调用
    fn execute_pump_cpi<'info>(
        &self,
        instruction: &Instruction,
        accounts: &[AccountInfo<'info>],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<()> {
        // 验证指令账户数量
        if instruction.accounts.len() > accounts.len() {
            msg!("账户数量不足: 需要 {}, 提供 {}", instruction.accounts.len(), accounts.len());
            return Err(RouteError::InvalidAccountCount.into());
        }

        // 映射账户到指令
        let mut account_infos = Vec::with_capacity(instruction.accounts.len());
        for i in 0..instruction.accounts.len() {
            if i < accounts.len() {
                account_infos.push(accounts[i].clone());
            } else {
                return Err(RouteError::InvalidAccountCount.into());
            }
        }

        // 记录CPI调用详情
        msg!("执行PumpSwap CPI: 程序 {}, 账户数 {}",
            instruction.program_id, account_infos.len());

        // 执行CPI调用
        match owner_seeds {
            Some(seeds) => {
                invoke_signed(instruction, &account_infos, seeds)
                    .map_err(|e| {
                        msg!("PumpSwap CPI调用失败 (带签名): {:?}", e);
                        RouteError::DexOperationFailed
                    })?
            }
            None => {
                invoke(instruction, &account_infos)
                    .map_err(|e| {
                        msg!("PumpSwap CPI调用失败: {:?}", e);
                        RouteError::DexOperationFailed
                    })?
            }
        }

        msg!("PumpSwap CPI调用成功");
        Ok(())
    }

    /// 创建PumpSwap交换参数
    pub fn create_pump_swap_params(
        pool: Pubkey,
        token_mint: Pubkey,
        creator: Pubkey,
        is_buy: bool,
        virtual_sol_reserves: u64,
        virtual_token_reserves: u64,
        real_sol_reserves: u64,
        real_token_reserves: u64,
        token_total_supply: u64,
        complete_marker_cap: u64,
    ) -> PumpSwapParams {
        PumpSwapParams {
            pool,
            token_mint,
            creator,
            is_buy,
            virtual_sol_reserves,
            virtual_token_reserves,
            real_sol_reserves,
            real_token_reserves,
            token_total_supply,
            complete_marker_cap,
        }
    }

    /// 获取当前bonding curve价格
    pub fn get_current_price(&self, params: &PumpSwapParams) -> Result<f64> {
        if params.virtual_token_reserves == 0 {
            return Err(RouteError::DivisionByZero.into());
        }

        let price = params.virtual_sol_reserves as f64 / params.virtual_token_reserves as f64;
        Ok(price)
    }

    /// 计算到达bonding curve完成所需的SOL
    pub fn calculate_sol_to_complete(&self, params: &PumpSwapParams) -> Result<u64> {
        if params.real_sol_reserves >= params.complete_marker_cap {
            return Ok(0); // 已经完成
        }

        params.complete_marker_cap
            .checked_sub(params.real_sol_reserves)
            .ok_or(RouteError::MathUnderflow.into())
    }
}

/// PumpSwap工具函数
pub mod utils {
    use super::*;

    /// 计算bonding curve当前阶段进度
    pub fn calculate_curve_progress(params: &PumpSwapParams) -> Result<f64> {
        if params.complete_marker_cap == 0 {
            return Ok(0.0);
        }

        let progress = params.real_sol_reserves as f64 / params.complete_marker_cap as f64;
        Ok(progress.min(1.0))
    }

    /// 验证PumpSwap池地址格式
    pub fn validate_pump_pool_address(pool: &Pubkey) -> Result<()> {
        if pool == &Pubkey::default() {
            return Err(RouteError::InvalidRouteConfig.into());
        }
        Ok(())
    }

    /// 计算市值（以SOL计价）
    pub fn calculate_market_cap_sol(params: &PumpSwapParams) -> Result<f64> {
        if params.virtual_token_reserves == 0 {
            return Ok(0.0);
        }

        let price = params.virtual_sol_reserves as f64 / params.virtual_token_reserves as f64;
        let market_cap = price * params.token_total_supply as f64;

        Ok(market_cap)
    }

    /// 计算滑点影响
    pub fn calculate_slippage_impact(
        virtual_reserves: u64,
        amount_in: u64,
    ) -> Result<u16> {
        if virtual_reserves == 0 {
            return Err(RouteError::DivisionByZero.into());
        }

        let impact = amount_in
            .checked_mul(10000)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(virtual_reserves.checked_add(amount_in).ok_or(RouteError::MathOverflow)?)
            .ok_or(RouteError::DivisionByZero.into())?;

        Ok(impact as u16)
    }

    /// 检查代币是否已毕业（完成bonding curve）
    pub fn is_token_graduated(params: &PumpSwapParams) -> bool {
        params.real_sol_reserves >= params.complete_marker_cap
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_pump_processor_creation() {
        let processor = PumpSwapProcessor::default();
        assert_eq!(processor.max_slippage_bps, 200);
        assert_eq!(processor.max_price_impact_bps, 1000);
        assert_eq!(processor.creator_fee_bps, 100);
        assert_eq!(processor.dex_name(), "PumpSwap");
        assert_eq!(processor.dex_type(), Dex::PumpSwap);
    }

    #[test]
    fn test_pump_swap_params_serialization() {
        let params = PumpSwapParams {
            pool: Pubkey::new_unique(),
            token_mint: Pubkey::new_unique(),
            creator: Pubkey::new_unique(),
            is_buy: true,
            virtual_sol_reserves: 1_000_000_000, // 1 SOL
            virtual_token_reserves: 1_000_000_000_000, // 1M tokens
            real_sol_reserves: 500_000_000, // 0.5 SOL
            real_token_reserves: 500_000_000_000, // 0.5M tokens
            token_total_supply: 1_000_000_000_000, // 1B tokens
            complete_marker_cap: 85_000_000_000, // 85 SOL
        };

        let serialized = params.try_to_vec().unwrap();
        let deserialized = PumpSwapParams::try_from_slice(&serialized).unwrap();

        assert_eq!(params.pool, deserialized.pool);
        assert_eq!(params.is_buy, deserialized.is_buy);
        assert_eq!(params.virtual_sol_reserves, deserialized.virtual_sol_reserves);
    }

    #[test]
    fn test_bonding_curve_buy_calculation() {
        let processor = PumpSwapProcessor::default();
        let params = PumpSwapParams {
            pool: Pubkey::new_unique(),
            token_mint: Pubkey::new_unique(),
            creator: Pubkey::new_unique(),
            is_buy: true,
            virtual_sol_reserves: 30_000_000_000, // 30 SOL
            virtual_token_reserves: 1_073_000_000_000_000, // 1.073B tokens
            real_sol_reserves: 10_000_000_000, // 10 SOL
            real_token_reserves: 400_000_000_000, // 400M tokens
            token_total_supply: 1_000_000_000_000_000, // 1B tokens
            complete_marker_cap: 85_000_000_000, // 85 SOL
        };

        // 测试买入1 SOL
        let sol_amount_in = 1_000_000_000; // 1 SOL
        let token_out = processor.calculate_buy_output(&params, sol_amount_in).unwrap();

        // 应该得到一些代币输出
        assert!(token_out > 0);
        assert!(token_out < params.virtual_token_reserves); // 输出应该小于虚拟储备

        println!("买入1 SOL得到 {} tokens", token_out);
    }

    #[test]
    fn test_bonding_curve_sell_calculation() {
        let processor = PumpSwapProcessor::default();
        let params = PumpSwapParams {
            pool: Pubkey::new_unique(),
            token_mint: Pubkey::new_unique(),
            creator: Pubkey::new_unique(),
            is_buy: false,
            virtual_sol_reserves: 30_000_000_000, // 30 SOL
            virtual_token_reserves: 1_073_000_000_000_000, // 1.073B tokens
            real_sol_reserves: 10_000_000_000, // 10 SOL
            real_token_reserves: 400_000_000_000, // 400M tokens
            token_total_supply: 1_000_000_000_000_000, // 1B tokens
            complete_marker_cap: 85_000_000_000, // 85 SOL
        };

        // 测试卖出1M tokens
        let token_amount_in = 1_000_000_000_000; // 1M tokens
        let sol_out = processor.calculate_sell_output(&params, token_amount_in).unwrap();

        // 应该得到一些SOL输出
        assert!(sol_out > 0);
        assert!(sol_out < params.virtual_sol_reserves); // 输出应该小于虚拟储备

        println!("卖出1M tokens得到 {} SOL", sol_out);
    }

    #[test]
    fn test_creator_fee_calculation() {
        let processor = PumpSwapProcessor::default();

        let fee = processor.calculate_creator_fee(1_000_000).unwrap(); // 1% of 1M
        assert_eq!(fee, 10_000); // 应该是10k
    }

    #[test]
    fn test_price_impact_validation() {
        let processor = PumpSwapProcessor::default();
        let params = PumpSwapParams {
            pool: Pubkey::new_unique(),
            token_mint: Pubkey::new_unique(),
            creator: Pubkey::new_unique(),
            is_buy: true,
            virtual_sol_reserves: 30_000_000_000, // 30 SOL
            virtual_token_reserves: 1_073_000_000_000_000, // 1.073B tokens
            real_sol_reserves: 10_000_000_000, // 10 SOL
            real_token_reserves: 400_000_000_000, // 400M tokens
            token_total_supply: 1_000_000_000_000_000, // 1B tokens
            complete_marker_cap: 85_000_000_000, // 85 SOL
        };

        // 小额交易应该通过
        let small_amount = 100_000_000; // 0.1 SOL
        assert!(processor.validate_price_impact(&params, small_amount, 1000).is_ok());

        // 大额交易应该被拒绝
        let large_amount = 10_000_000_000; // 10 SOL (33% of virtual reserves)
        assert!(processor.validate_price_impact(&params, large_amount, 1000).is_err());
    }

    #[test]
    fn test_bonding_curve_completion() {
        let processor = PumpSwapProcessor::default();
        let params = PumpSwapParams {
            pool: Pubkey::new_unique(),
            token_mint: Pubkey::new_unique(),
            creator: Pubkey::new_unique(),
            is_buy: true,
            virtual_sol_reserves: 30_000_000_000, // 30 SOL
            virtual_token_reserves: 1_073_000_000_000_000, // 1.073B tokens
            real_sol_reserves: 80_000_000_000, // 80 SOL (接近完成)
            real_token_reserves: 400_000_000_000, // 400M tokens
            token_total_supply: 1_000_000_000_000_000, // 1B tokens
            complete_marker_cap: 85_000_000_000, // 85 SOL
        };

        // 检查是否可以完成bonding curve
        let amount_needed = processor.calculate_sol_to_complete(&params).unwrap();
        assert_eq!(amount_needed, 5_000_000_000); // 需要5 SOL

        let can_complete = processor.can_complete_bonding_curve(&params, 6_000_000_000).unwrap(); // 买入6 SOL
        assert!(can_complete);
    }

    #[test]
    fn test_utils_functions() {
        let params = PumpSwapParams {
            pool: Pubkey::new_unique(),
            token_mint: Pubkey::new_unique(),
            creator: Pubkey::new_unique(),
            is_buy: true,
            virtual_sol_reserves: 30_000_000_000, // 30 SOL
            virtual_token_reserves: 1_073_000_000_000_000, // 1.073B tokens
            real_sol_reserves: 42_500_000_000, // 42.5 SOL (50%进度)
            real_token_reserves: 400_000_000_000, // 400M tokens
            token_total_supply: 1_000_000_000_000_000, // 1B tokens
            complete_marker_cap: 85_000_000_000, // 85 SOL
        };

        // 测试进度计算
        let progress = utils::calculate_curve_progress(&params).unwrap();
        assert!((progress - 0.5).abs() < 0.01); // 应该是50%

        // 测试市值计算
        let market_cap = utils::calculate_market_cap_sol(&params).unwrap();
        assert!(market_cap > 0.0);

        // 测试滑点影响
        let slippage = utils::calculate_slippage_impact(30_000_000_000, 1_000_000_000).unwrap();
        assert!(slippage > 0 && slippage < 10000); // 应该在合理范围内

        // 测试是否毕业
        assert!(!utils::is_token_graduated(&params)); // 还没完成

        // 测试地址验证
        assert!(utils::validate_pump_pool_address(&Pubkey::default()).is_err());
        assert!(utils::validate_pump_pool_address(&Pubkey::new_unique()).is_ok());
    }
}
