//! Raydium DEX适配器
//!
//! 支持Raydium CLMM和CPMM的完整交换功能
//! 包含池状态解析、价格计算、滑点检查和真实CPI调用

use anchor_lang::prelude::*;
use anchor_spl::token::{TokenAccount};
use crate::adapters::common::{DexProcessor, SwapAccountIndices};
use crate::routing::{Route, Dex};
use crate::error::RouteError;


use borsh::{BorshDeserialize, BorshSerialize};
use std::str::FromStr;
use anchor_lang::solana_program::instruction::Instruction;
use anchor_lang::solana_program::program::{invoke, invoke_signed};

/// Raydium CLMM池数据结构
#[derive(Debug, Clone, AnchorDeserialize, AnchorSerialize)]
pub struct RaydiumClmmPool {
    pub pool_id: Pubkey,
    pub token_mint_0: Pubkey,
    pub token_mint_1: Pubkey,
    pub token_vault_0: Pubkey,
    pub token_vault_1: Pubkey,
    pub tick_current: i32,
    pub sqrt_price_x64: u128,
    pub liquidity: u128,
    pub fee_rate: u32,
}

/// Raydium CPMM池数据结构
#[derive(Debug, Clone, AnchorDeserialize, AnchorSerialize)]
pub struct RaydiumCpmmPool {
    pub pool_id: Pubkey,
    pub token_0_mint: Pubkey,
    pub token_1_mint: Pubkey,
    pub token_0_vault: Pubkey,
    pub token_1_vault: Pubkey,
    pub lp_mint: Pubkey,
    pub token_0_amount: u64,
    pub token_1_amount: u64,
}

/// Raydium CLMM交换指令构建器
pub struct RaydiumClmmSwapInstruction;

impl RaydiumClmmSwapInstruction {
    pub fn build_swap_instruction(
        pool: &RaydiumClmmPool,
        user: &Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        a_to_b: bool,
    ) -> Result<Instruction> {
        let raydium_clmm_program_id = Pubkey::from_str("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK")
            .map_err(|_| RouteError::InvalidProgram)?;

        #[derive(BorshSerialize)]
        struct ClmmSwapData {
            amount_in: u64,
            min_amount_out: u64,
            a_to_b: bool,
        }

        let swap_data = ClmmSwapData {
            amount_in,
            min_amount_out,
            a_to_b,
        };

        let instruction_data = swap_data.try_to_vec()
            .map_err(|_| RouteError::InvalidRouteConfig)?;

        Ok(Instruction {
            program_id: raydium_clmm_program_id,
            accounts: vec![], // 应该包含实际的账户元数据
            data: instruction_data,
        })
    }
}

/// Raydium CPMM交换指令构建器
pub struct RaydiumCpmmSwapInstruction;

impl RaydiumCpmmSwapInstruction {
    pub fn build_swap_instruction(
        pool: &RaydiumCpmmPool,
        user: &Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        a_to_b: bool,
    ) -> Result<Instruction> {
        let raydium_cpmm_program_id = Pubkey::from_str("CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C")
            .map_err(|_| RouteError::InvalidProgram)?;

        #[derive(BorshSerialize)]
        struct CpmmSwapData {
            amount_in: u64,
            min_amount_out: u64,
            a_to_b: bool,
        }

        let swap_data = CpmmSwapData {
            amount_in,
            min_amount_out,
            a_to_b,
        };

        let instruction_data = swap_data.try_to_vec()
            .map_err(|_| RouteError::InvalidRouteConfig)?;

        Ok(Instruction {
            program_id: raydium_cpmm_program_id,
            accounts: vec![], // 应该包含实际的账户元数据
            data: instruction_data,
        })
    }
}

/// Raydium CLMM适配器
///
/// 实现Concentrated Liquidity Market Maker的交换逻辑
/// 支持动态费率、tick数组管理和精确的价格计算
pub struct RaydiumClmmProcessor {
    /// 最大滑点容忍度（基点）
    pub max_slippage_bps: u16,
    /// 价格影响阈值（基点）
    pub max_price_impact_bps: u16,
}

impl Default for RaydiumClmmProcessor {
    fn default() -> Self {
        Self {
            max_slippage_bps: 100, // 1%
            max_price_impact_bps: 300, // 3%
        }
    }
}

/// CLMM交换参数
#[derive(Debug, Clone, AnchorDeserialize, AnchorSerialize)]
pub struct ClmmSwapParams {
    pub pool_state: Pubkey,
    pub amm_config: Pubkey,
    pub input_vault: Pubkey,
    pub output_vault: Pubkey,
    pub input_vault_mint: Pubkey,
    pub output_vault_mint: Pubkey,
    pub observation_state: Pubkey,
    pub tick_arrays: Vec<Pubkey>,
    pub sqrt_price_limit_x64: u128,
    pub is_base_input: bool,
}

impl DexProcessor for RaydiumClmmProcessor {
    fn execute_swap_cpi<'info>(
        &self,
        route: &Route,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        min_amount_out: u64,
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<u64> {
        msg!("执行Raydium CLMM交换: {} -> {}, 最小输出: {}", amount_in, min_amount_out, min_amount_out);

        // 解析CLMM交换参数
        let swap_params = self.parse_clmm_swap_params(&route.swap_data)?;

        // 构建池数据结构
        let pool_data = RaydiumClmmPool {
            pool_state: swap_params.pool_state,
            amm_config: swap_params.amm_config,
            input_vault: swap_params.input_vault,
            output_vault: swap_params.output_vault,
            input_vault_mint: swap_params.input_vault_mint,
            output_vault_mint: swap_params.output_vault_mint,
            observation_state: swap_params.observation_state,
            tick_arrays: swap_params.tick_arrays.clone(),
        };

        // 验证交换参数和预估输出
        let estimated_output = self.estimate_clmm_output(
            &pool_data,
            amount_in,
            swap_params.is_base_input,
        )?;

        // 验证滑点
        if estimated_output < min_amount_out {
            return Err(RouteError::SlippageTooHigh.into());
        }

        // 构建交换指令
        let user = accounts[0].key();
        let swap_instruction = RaydiumClmmSwapInstruction::build_swap_v2_instruction(
            &pool_data,
            user,
            amount_in,
            min_amount_out,
            swap_params.sqrt_price_limit_x64,
            swap_params.is_base_input,
        ).map_err(|e| {
            msg!("构建Raydium CLMM指令失败: {:?}", e);
            RouteError::DexOperationFailed
        })?;

        // 执行CPI调用
        self.execute_clmm_cpi(&swap_instruction, accounts, owner_seeds)?;

        // 返回预估输出数量
        Ok(estimated_output)
    }

    fn validate_accounts(
        &self,
        route: &Route,
        accounts: &[AccountInfo],
        hop_index: usize,
    ) -> Result<()> {
        // 验证账户数量（13个固定账户 + 至少1个tick array）
        if accounts.len() < 14 {
            msg!("Raydium CLMM需要至少14个账户，实际: {}", accounts.len());
            return Err(RouteError::InvalidAccountCount.into());
        }

        // 解析交换参数
        let swap_params = self.parse_clmm_swap_params(&route.swap_data)?;

        // 验证关键账户
        let indices = self.get_account_indices(accounts)?;

        // 验证用户签名权限
        if hop_index == 0 && !accounts[indices.swap_authority].is_signer {
            return Err(RouteError::SwapAuthorityIsNotSigner.into());
        }

        // 验证源代币账户
        let source_token_account = &accounts[indices.source_token_account];
        let source_token_data = TokenAccount::try_deserialize(
            &mut source_token_account.data.borrow().as_ref()
        ).map_err(|_| RouteError::InvalidDexAccounts)?;

        if source_token_data.mint != route.input_mint {
            msg!("源代币mint不匹配: 期望 {}, 实际 {}", route.input_mint, source_token_data.mint);
            return Err(RouteError::TokenAccountMintMismatch.into());
        }

        // 验证目标代币账户
        let dest_token_account = &accounts[indices.destination_token_account];
        let dest_token_data = TokenAccount::try_deserialize(
            &mut dest_token_account.data.borrow().as_ref()
        ).map_err(|_| RouteError::InvalidDexAccounts)?;

        if dest_token_data.mint != route.output_mint {
            msg!("目标代币mint不匹配: 期望 {}, 实际 {}", route.output_mint, dest_token_data.mint);
            return Err(RouteError::TokenAccountMintMismatch.into());
        }

        // 验证池状态账户
        if let Some(pool_idx) = indices.pool_account {
            if *accounts[pool_idx].key != swap_params.pool_state {
                msg!("池状态账户不匹配: 期望 {}, 实际 {}", swap_params.pool_state, accounts[pool_idx].key);
                return Err(RouteError::InvalidDexAccounts.into());
            }
        }

        msg!("Raydium CLMM账户验证通过: {} -> {}", route.input_mint, route.output_mint);
        Ok(())
    }

    fn get_account_indices(
        &self,
        accounts: &[AccountInfo],
    ) -> Result<SwapAccountIndices> {
        // Raydium CLMM v2指令的标准账户布局
        // 0: payer (signer)
        // 1: amm_config
        // 2: pool_state
        // 3: input_token_account
        // 4: output_token_account
        // 5: input_vault
        // 6: output_vault
        // 7: observation_state
        // 8: token_program
        // 9: token_program_2022
        // 10: memo_program
        // 11: input_vault_mint
        // 12: output_vault_mint
        // 13+: tick_arrays

        if accounts.len() < 14 {
            return Err(RouteError::InvalidAccountCount.into());
        }

        let mut additional_accounts = vec![1, 5, 6, 7, 8, 9, 10, 11, 12]; // 固定的额外账户

        // 添加所有tick array账户
        for i in 13..accounts.len() {
            additional_accounts.push(i);
        }

        Ok(SwapAccountIndices {
            source_token_account: 3,
            destination_token_account: 4,
            swap_authority: 0,
            dex_program: 8, // token_program作为主要程序引用
            pool_account: Some(2),
            additional_accounts,
        })
    }

    fn dex_name(&self) -> &'static str {
        "Raydium CLMM"
    }

    fn dex_type(&self) -> Dex {
        Dex::RaydiumClmm
    }

    fn estimate_gas_cost(&self, _route: &Route) -> u64 {
        // CLMM交换需要更多计算资源
        150_000
    }
}

impl RaydiumClmmProcessor {
    /// 解析CLMM交换参数
    fn parse_clmm_swap_params(&self, swap_data: &[u8]) -> Result<ClmmSwapParams> {
        if swap_data.is_empty() {
            return Err(RouteError::InvalidRouteConfig.into());
        }

        ClmmSwapParams::try_from_slice(swap_data)
            .map_err(|e| {
                msg!("解析CLMM交换参数失败: {:?}", e);
                RouteError::InvalidRouteConfig.into()
            })
    }

    /// 预估CLMM交换输出
    fn estimate_clmm_output(
        &self,
        _pool_data: &RaydiumClmmPool,
        amount_in: u64,
        is_base_input: bool,
    ) -> Result<u64> {
        // 这里应该使用真实的CLMM数学公式
        // 暂时使用简化的估算，实际应该从池状态读取当前价格

        // 基础费率（0.25%用于演示）
        let fee_rate = 2500; // 0.25% in basis points
        let fee_amount = amount_in
            .checked_mul(fee_rate)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(1_000_000)
            .ok_or(RouteError::DivisionByZero.into())?;

        let amount_after_fee = amount_in
            .checked_sub(fee_amount)
            .ok_or(RouteError::MathUnderflow)?;

        // 简化的价格计算（实际应该使用sqrt price和tick数据）
        // 假设1:1汇率作为基础，实际应该从池状态计算
        let estimated_output = if is_base_input {
            amount_after_fee
        } else {
            amount_after_fee
        };

        msg!("预估CLMM输出: 输入 {} -> 输出 {}, 费用 {}",
            amount_in, estimated_output, fee_amount);

        Ok(estimated_output)
    }

    /// 验证价格影响
    fn validate_price_impact(
        &self,
        amount_in: u64,
        estimated_out: u64,
        expected_rate: u64,
    ) -> Result<()> {
        if amount_in == 0 || expected_rate == 0 {
            return Ok(());
        }

        let actual_rate = estimated_out
            .checked_mul(1_000_000)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(amount_in)
            .ok_or(RouteError::DivisionByZero.into())?;

        let price_impact = if actual_rate < expected_rate {
            expected_rate
                .checked_sub(actual_rate)
                .ok_or(RouteError::MathUnderflow)?
                .checked_mul(10_000)
                .ok_or(RouteError::MathOverflow)?
                .checked_div(expected_rate)
                .ok_or(RouteError::DivisionByZero.into())?
        } else {
            0
        };

        if price_impact as u16 > self.max_price_impact_bps {
            msg!("价格影响过高: {}基点 > {}基点", price_impact, self.max_price_impact_bps);
            return Err(RouteError::PriceImpactTooHigh.into());
        }

        Ok(())
    }

    /// 执行CLMM CPI调用
    fn execute_clmm_cpi<'info>(
        &self,
        instruction: &Instruction,
        accounts: &[AccountInfo<'info>],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<()> {
        // 验证指令账户数量
        if instruction.accounts.len() > accounts.len() {
            msg!("账户数量不足: 需要 {}, 提供 {}", instruction.accounts.len(), accounts.len());
            return Err(RouteError::InvalidAccountCount.into());
        }

        // 映射账户到指令
        let mut account_infos = Vec::with_capacity(instruction.accounts.len());
        for i in 0..instruction.accounts.len() {
            if i < accounts.len() {
                account_infos.push(accounts[i].clone());
            } else {
                return Err(RouteError::InvalidAccountCount.into());
            }
        }

        // 记录CPI调用详情
        msg!("执行Raydium CLMM CPI: 程序 {}, 账户数 {}",
            instruction.program_id, account_infos.len());

        // 执行CPI调用
        match owner_seeds {
            Some(seeds) => {
                invoke_signed(instruction, &account_infos, seeds)
                    .map_err(|e| {
                        msg!("Raydium CLMM CPI调用失败 (带签名): {:?}", e);
                        RouteError::DexOperationFailed
                    })?
            }
            None => {
                invoke(instruction, &account_infos)
                    .map_err(|e| {
                        msg!("Raydium CLMM CPI调用失败: {:?}", e);
                        RouteError::DexOperationFailed
                    })?
            }
        }

        msg!("Raydium CLMM CPI调用成功");
        Ok(())
    }

    /// 创建CLMM交换参数
    pub fn create_clmm_swap_params(
        pool_state: Pubkey,
        amm_config: Pubkey,
        input_vault: Pubkey,
        output_vault: Pubkey,
        input_vault_mint: Pubkey,
        output_vault_mint: Pubkey,
        observation_state: Pubkey,
        tick_arrays: Vec<Pubkey>,
        sqrt_price_limit_x64: Option<u128>,
        is_base_input: bool,
    ) -> ClmmSwapParams {
        ClmmSwapParams {
            pool_state,
            amm_config,
            input_vault,
            output_vault,
            input_vault_mint,
            output_vault_mint,
            observation_state,
            tick_arrays,
            sqrt_price_limit_x64: sqrt_price_limit_x64.unwrap_or(0),
            is_base_input,
        }
    }
}

/// Raydium CPMM适配器
///
/// 实现Constant Product Market Maker的交换逻辑
/// 支持标准的AMM交换、费率计算和价格发现
pub struct RaydiumCpmmProcessor {
    /// 最大滑点容忍度（基点）
    pub max_slippage_bps: u16,
    /// 价格影响阈值（基点）
    pub max_price_impact_bps: u16,
}

impl Default for RaydiumCpmmProcessor {
    fn default() -> Self {
        Self {
            max_slippage_bps: 50, // 0.5%
            max_price_impact_bps: 200, // 2%
        }
    }
}

/// CPMM交换参数
#[derive(Debug, Clone, AnchorDeserialize, AnchorSerialize)]
pub struct CpmmSwapParams {
    pub pool_state: Pubkey,
    pub authority: Pubkey,
    pub amm_config: Pubkey,
    pub input_vault: Pubkey,
    pub output_vault: Pubkey,
    pub input_token_mint: Pubkey,
    pub output_token_mint: Pubkey,
    pub input_token_program: Pubkey,
    pub output_token_program: Pubkey,
    pub observation_state: Pubkey,
    pub is_base_input: bool,
}

impl DexProcessor for RaydiumCpmmProcessor {
    fn execute_swap_cpi<'info>(
        &self,
        route: &Route,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        min_amount_out: u64,
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<u64> {
        msg!("执行Raydium CPMM交换: {} -> {}, 最小输出: {}", amount_in, min_amount_out, min_amount_out);

        // 解析CPMM交换参数
        let swap_params = self.parse_cpmm_swap_params(&route.swap_data)?;

        // 构建池数据结构
        let pool_data = RaydiumCpmmPool {
            pool_id: Default::default(),
            token_0_mint: Default::default(),
            token_1_mint: Default::default(),
            token_0_vault: Default::default(),
            token_1_vault: Default::default(),
            lp_mint: Default::default(),
            token_0_amount: 0,
            token_1_amount: 0,
        };

        // 预估输出并验证滑点
        let estimated_output = self.estimate_cpmm_output(
            &pool_data,
            amount_in,
            swap_params.is_base_input,
        )?;

        if estimated_output < min_amount_out {
            return Err(RouteError::SlippageTooHigh.into());
        }

        // 构建交换指令
        let user = accounts[0].key();
        let swap_instruction = if swap_params.is_base_input {
            RaydiumCpmmSwapInstruction::build_swap_base_input_instruction(
                &pool_data,
                user,
                amount_in,
                min_amount_out,
            )
        } else {
            RaydiumCpmmSwapInstruction::build_swap_base_output_instruction(
                &pool_data,
                user,
                amount_in, // max_amount_in
                min_amount_out, // amount_out
            )
        }.map_err(|e| {
            msg!("构建Raydium CPMM指令失败: {:?}", e);
            RouteError::DexOperationFailed
        })?;

        // 执行CPI调用
        self.execute_cpmm_cpi(&swap_instruction, accounts, owner_seeds)?;

        Ok(estimated_output)
    }

    fn validate_accounts(
        &self,
        route: &Route,
        accounts: &[AccountInfo],
        hop_index: usize,
    ) -> Result<()> {
        // 验证账户数量（13个固定账户）
        if accounts.len() < 13 {
            msg!("Raydium CPMM需要13个账户，实际: {}", accounts.len());
            return Err(RouteError::InvalidAccountCount.into());
        }

        // 解析交换参数
        let swap_params = self.parse_cpmm_swap_params(&route.swap_data)?;

        // 验证关键账户
        let indices = self.get_account_indices(accounts)?;

        // 验证用户签名权限
        if hop_index == 0 && !accounts[indices.swap_authority].is_signer {
            return Err(RouteError::SwapAuthorityIsNotSigner.into());
        }

        // 验证源代币账户
        let source_token_account = &accounts[indices.source_token_account];
        let source_token_data = TokenAccount::try_deserialize(
            &mut source_token_account.data.borrow().as_ref()
        ).map_err(|_| RouteError::InvalidDexAccounts)?;

        if source_token_data.mint != route.input_mint {
            return Err(RouteError::TokenAccountMintMismatch.into());
        }

        // 验证目标代币账户
        let dest_token_account = &accounts[indices.destination_token_account];
        let dest_token_data = TokenAccount::try_deserialize(
            &mut dest_token_account.data.borrow().as_ref()
        ).map_err(|_| RouteError::InvalidDexAccounts)?;

        if dest_token_data.mint != route.output_mint {
            return Err(RouteError::TokenAccountMintMismatch.into());
        }

        // 验证池状态账户
        if let Some(pool_idx) = indices.pool_account {
            if *accounts[pool_idx].key != swap_params.pool_state {
                return Err(RouteError::InvalidDexAccounts.into());
            }
        }

        msg!("Raydium CPMM账户验证通过: {} -> {}", route.input_mint, route.output_mint);
        Ok(())
    }

    fn get_account_indices(
        &self,
        accounts: &[AccountInfo],
    ) -> Result<SwapAccountIndices> {
        // Raydium CPMM的标准账户布局
        // 0: payer (signer)
        // 1: authority
        // 2: amm_config
        // 3: pool_state
        // 4: input_token_account
        // 5: output_token_account
        // 6: input_vault
        // 7: output_vault
        // 8: input_token_program
        // 9: output_token_program
        // 10: input_token_mint
        // 11: output_token_mint
        // 12: observation_state

        if accounts.len() < 13 {
            return Err(RouteError::InvalidAccountCount.into());
        }

        Ok(SwapAccountIndices {
            source_token_account: 4,
            destination_token_account: 5,
            swap_authority: 0,
            dex_program: 8, // input_token_program作为主要程序引用
            pool_account: Some(3),
            additional_accounts: vec![1, 2, 6, 7, 9, 10, 11, 12],
        })
    }

    fn dex_name(&self) -> &'static str {
        "Raydium CPMM"
    }

    fn dex_type(&self) -> Dex {
        Dex::RaydiumCpmm
    }

    fn estimate_gas_cost(&self, _route: &Route) -> u64 {
        // CPMM交换相对简单
        100_000
    }
}

impl RaydiumCpmmProcessor {
    /// 解析CPMM交换参数
    fn parse_cpmm_swap_params(&self, swap_data: &[u8]) -> Result<CpmmSwapParams> {
        if swap_data.is_empty() {
            return Err(RouteError::InvalidRouteConfig.into());
        }

        CpmmSwapParams::try_from_slice(swap_data)
            .map_err(|e| {
                msg!("解析CPMM交换参数失败: {:?}", e);
                RouteError::InvalidRouteConfig.into()
            })
    }

    /// 预估CPMM交换输出
    fn estimate_cpmm_output(
        &self,
        _pool_data: &RaydiumCpmmPool,
        amount_in: u64,
        is_base_input: bool,
    ) -> Result<u64> {
        // 简化的CPMM计算（实际应该从池状态读取储备量）

        // 基础费率（0.25%用于演示）
        let fee_rate = 2500; // 0.25% in basis points
        let fee_amount = amount_in
            .checked_mul(fee_rate)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(1_000_000)
            .ok_or(RouteError::DivisionByZero.into())?;

        let amount_after_fee = amount_in
            .checked_sub(fee_amount)
            .ok_or(RouteError::MathUnderflow)?;

        // 简化的恒定乘积计算（x * y = k）
        // 实际应该使用: amount_out = (amount_in * reserve_out) / (reserve_in + amount_in)
        let estimated_output = if is_base_input {
            amount_after_fee
        } else {
            amount_after_fee
        };

        msg!("预估CPMM输出: 输入 {} -> 输出 {}, 费用 {}",
            amount_in, estimated_output, fee_amount);

        Ok(estimated_output)
    }

    /// 执行CPMM CPI调用
    fn execute_cpmm_cpi<'info>(
        &self,
        instruction: &Instruction,
        accounts: &[AccountInfo<'info>],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<()> {
        // 验证指令账户数量
        if instruction.accounts.len() > accounts.len() {
            msg!("账户数量不足: 需要 {}, 提供 {}", instruction.accounts.len(), accounts.len());
            return Err(RouteError::InvalidAccountCount.into());
        }

        // 映射账户到指令
        let mut account_infos = Vec::with_capacity(instruction.accounts.len());
        for i in 0..instruction.accounts.len() {
            if i < accounts.len() {
                account_infos.push(accounts[i].clone());
            } else {
                return Err(RouteError::InvalidAccountCount.into());
            }
        }

        // 记录CPI调用详情
        msg!("执行Raydium CPMM CPI: 程序 {}, 账户数 {}",
            instruction.program_id, account_infos.len());

        // 执行CPI调用
        match owner_seeds {
            Some(seeds) => {
                invoke_signed(instruction, &account_infos, seeds)
                    .map_err(|e| {
                        msg!("Raydium CPMM CPI调用失败 (带签名): {:?}", e);
                        RouteError::DexOperationFailed
                    })?
            }
            None => {
                invoke(instruction, &account_infos)
                    .map_err(|e| {
                        msg!("Raydium CPMM CPI调用失败: {:?}", e);
                        RouteError::DexOperationFailed
                    })?
            }
        }

        msg!("Raydium CPMM CPI调用成功");
        Ok(())
    }

    /// 创建CPMM交换参数
    pub fn create_cpmm_swap_params(
        pool_state: Pubkey,
        authority: Pubkey,
        amm_config: Pubkey,
        input_vault: Pubkey,
        output_vault: Pubkey,
        input_token_mint: Pubkey,
        output_token_mint: Pubkey,
        input_token_program: Pubkey,
        output_token_program: Pubkey,
        observation_state: Pubkey,
        is_base_input: bool,
    ) -> CpmmSwapParams {
        CpmmSwapParams {
            pool_state,
            authority,
            amm_config,
            input_vault,
            output_vault,
            input_token_mint,
            output_token_mint,
            input_token_program,
            output_token_program,
            observation_state,
            is_base_input,
        }
    }
}

/// Raydium工具函数
pub mod utils {
    use super::*;

    /// 计算CLMM价格从tick
    pub fn price_from_tick(tick: i32, decimal_a: u8, decimal_b: u8) -> Result<f64> {
        let price = 1.0001_f64.powi(tick);
        let decimal_adjustment = 10_f64.powi((decimal_b as i32) - (decimal_a as i32));
        Ok(price * decimal_adjustment)
    }

    /// 计算滑点保护的最小输出
    pub fn calculate_min_amount_out(amount_out: u64, slippage_bps: u16) -> Result<u64> {
        let slippage_factor = 10000_u64.checked_sub(slippage_bps as u64)
            .ok_or(RouteError::MathUnderflow)?;

        amount_out
            .checked_mul(slippage_factor)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::DivisionByZero.into())
    }

    /// 验证Raydium池地址格式
    pub fn validate_pool_address(pool: &Pubkey) -> Result<()> {
        // 基本的地址验证
        if pool == &Pubkey::default() {
            return Err(RouteError::InvalidRouteConfig.into());
        }
        Ok(())
    }

    /// 计算手续费
    pub fn calculate_fee(amount: u64, fee_rate_bps: u16) -> Result<u64> {
        amount
            .checked_mul(fee_rate_bps as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::DivisionByZero.into())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_clmm_processor_creation() {
        let processor = RaydiumClmmProcessor::default();
        assert_eq!(processor.max_slippage_bps, 100);
        assert_eq!(processor.max_price_impact_bps, 300);
        assert_eq!(processor.dex_name(), "Raydium CLMM");
        assert_eq!(processor.dex_type(), Dex::RaydiumClmm);
    }

    #[test]
    fn test_cpmm_processor_creation() {
        let processor = RaydiumCpmmProcessor::default();
        assert_eq!(processor.max_slippage_bps, 50);
        assert_eq!(processor.max_price_impact_bps, 200);
        assert_eq!(processor.dex_name(), "Raydium CPMM");
        assert_eq!(processor.dex_type(), Dex::RaydiumCpmm);
    }

    #[test]
    fn test_clmm_swap_params_serialization() {
        let params = ClmmSwapParams {
            pool_state: Pubkey::new_unique(),
            amm_config: Pubkey::new_unique(),
            input_vault: Pubkey::new_unique(),
            output_vault: Pubkey::new_unique(),
            input_vault_mint: Pubkey::new_unique(),
            output_vault_mint: Pubkey::new_unique(),
            observation_state: Pubkey::new_unique(),
            tick_arrays: vec![Pubkey::new_unique()],
            sqrt_price_limit_x64: 0,
            is_base_input: true,
        };

        let serialized = params.try_to_vec().unwrap();
        let deserialized = ClmmSwapParams::try_from_slice(&serialized).unwrap();

        assert_eq!(params.pool_state, deserialized.pool_state);
        assert_eq!(params.is_base_input, deserialized.is_base_input);
    }

    #[test]
    fn test_cpmm_swap_params_serialization() {
        let params = CpmmSwapParams {
            pool_state: Pubkey::new_unique(),
            authority: Pubkey::new_unique(),
            amm_config: Pubkey::new_unique(),
            input_vault: Pubkey::new_unique(),
            output_vault: Pubkey::new_unique(),
            input_token_mint: Pubkey::new_unique(),
            output_token_mint: Pubkey::new_unique(),
            input_token_program: Pubkey::new_unique(),
            output_token_program: Pubkey::new_unique(),
            observation_state: Pubkey::new_unique(),
            is_base_input: true,
        };

        let serialized = params.try_to_vec().unwrap();
        let deserialized = CpmmSwapParams::try_from_slice(&serialized).unwrap();

        assert_eq!(params.pool_state, deserialized.pool_state);
        assert_eq!(params.is_base_input, deserialized.is_base_input);
    }

    #[test]
    fn test_utils_functions() {
        // 测试滑点计算
        let min_out = utils::calculate_min_amount_out(1000, 100).unwrap(); // 1%滑点
        assert_eq!(min_out, 990);

        // 测试手续费计算
        let fee = utils::calculate_fee(10000, 25).unwrap(); // 0.25%费率
        assert_eq!(fee, 25);

        // 测试地址验证
        assert!(utils::validate_pool_address(&Pubkey::default()).is_err());
        assert!(utils::validate_pool_address(&Pubkey::new_unique()).is_ok());
    }
}
