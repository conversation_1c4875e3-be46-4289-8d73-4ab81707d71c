//! 通用DEX适配器特征和工具
//!
//! 定义所有DEX适配器必须实现的统一接口

use anchor_lang::prelude::*;
use anchor_spl::token::{TokenAccount, Token};
use anchor_spl::associated_token::AssociatedToken;
use crate::error::RouteError;
use crate::routing::types::{Route, Dex};

/// DEX处理器统一接口
/// 所有DEX适配器都必须实现此特征
pub trait DexProcessor {
    /// 交换前的预处理
    ///
    /// # 参数
    /// * `route` - 路由配置信息
    /// * `account_infos` - 相关账户信息
    /// * `hop_index` - 当前步骤索引
    ///
    /// # 返回
    /// 返回交换前的余额和准备好的账户索引
    fn before_swap(
        &self,
        _route: &Route,
        account_infos: &[AccountInfo],
        _hop_index: usize,
    ) -> Result<(u64, SwapAccountIndices)> {
        // 默认实现：查询输出代币账户的余额
        let indices = self.get_account_indices(account_infos)?;
        let balance = self.get_token_balance(
            &account_infos[indices.destination_token_account]
        )?;
        Ok((balance, indices))
    }

    /// 交换后的处理
    ///
    /// # 参数
    /// * `route` - 路由配置信息
    /// * `account_infos` - 相关账户信息
    /// * `hop_index` - 当前步骤索引
    /// * `owner_seeds` - 所有者种子（用于PDA签名）
    /// * `before_balance` - 交换前余额
    /// * `account_indices` - 账户索引信息
    ///
    /// # 返回
    /// 返回实际的输出数量
    fn after_swap(
        &self,
        _route: &Route,
        account_infos: &[AccountInfo],
        _hop_index: usize,
        _owner_seeds: Option<&[&[&[u8]]]>,
        before_balance: u64,
        account_indices: &SwapAccountIndices,
    ) -> Result<u64> {
        // 查询交换后的余额
        let after_balance = self.get_token_balance(
            &account_infos[account_indices.destination_token_account]
        )?;

        let output_amount = after_balance.checked_sub(before_balance)
            .ok_or(RouteError::MathUnderflow)?;

        // 验证输出数量满足最小要求
        if output_amount < route.min_amount_out {
            return Err(RouteError::SlippageTooHigh.into());
        }

        msg!("第{}步 {} 交换完成: {} -> {}",
            hop_index + 1, self.dex_name(), before_balance, after_balance);

        Ok(output_amount)
    }

    /// 执行实际的CPI调用
    ///
    /// # 参数
    /// * `route` - 路由配置
    /// * `accounts` - DEX特定的账户列表
    /// * `amount_in` - 输入数量
    /// * `min_amount_out` - 最小输出数量（滑点保护）
    /// * `owner_seeds` - PDA签名种子
    ///
    /// # 返回
    /// 返回预期的输出数量
    fn execute_swap_cpi<'info>(
        &self,
        route: &Route,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        min_amount_out: u64,
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<u64>;

    /// 验证账户结构是否正确
    ///
    /// # 参数
    /// * `route` - 路由配置
    /// * `accounts` - 要验证的账户列表
    /// * `hop_index` - 当前步骤索引
    fn validate_accounts(
        &self,
        route: &Route,
        accounts: &[AccountInfo],
        hop_index: usize,
    ) -> Result<()>;

    /// 获取账户在数组中的索引位置
    fn get_account_indices(&self, accounts: &[AccountInfo]) -> Result<SwapAccountIndices>;

    /// 获取代币账户余额
    fn get_token_balance(&self, token_account: &AccountInfo) -> Result<u64> {
        let account_data = TokenAccount::try_deserialize(&mut token_account.data.borrow().as_ref())?;
        Ok(account_data.amount)
    }

    /// 验证代币账户的mint和所有者
    fn validate_token_account(
        &self,
        token_account: &AccountInfo,
        expected_mint: &Pubkey,
        expected_owner: &Pubkey,
    ) -> Result<()> {
        let account_data = TokenAccount::try_deserialize(&mut token_account.data.borrow().as_ref())?;

        if account_data.mint != *expected_mint {
            return Err(RouteError::TokenAccountMintMismatch.into());
        }

        if account_data.owner != *expected_owner {
            return Err(RouteError::TokenAccountOwnerMismatch.into());
        }

        Ok(())
    }

    /// 获取DEX的名称（用于日志和调试）
    fn dex_name(&self) -> &'static str;

    /// 获取DEX类型
    fn dex_type(&self) -> Dex;

    /// 估算Gas消耗
    fn estimate_gas_cost(&self, _route: &Route) -> u64 {
        // 默认估算，不同DEX可以覆盖
        match self.dex_type() {
            Dex::RaydiumClmm => 120_000,
            Dex::RaydiumCpmm => 80_000,
            Dex::MeteoraLb => 100_000,
            Dex::MeteoraAmm => 70_000,
            Dex::Orca => 90_000,
            Dex::PumpSwap => 60_000,
        }
    }
}

/// 交换账户索引结构
/// 用于记录各种账户在账户数组中的位置
#[derive(Debug, Clone)]
pub struct SwapAccountIndices {
    /// 源代币账户索引
    pub source_token_account: usize,
    /// 目标代币账户索引
    pub destination_token_account: usize,
    /// 交换权限账户索引
    pub swap_authority: usize,
    /// DEX程序账户索引
    pub dex_program: usize,
    /// 池子账户索引（如果适用）
    pub pool_account: Option<usize>,
    /// 其他DEX特定账户索引
    pub additional_accounts: Vec<usize>,
}

/// 通用的路由处理器特征
pub trait CommonRouteProcessor<'info> {
    /// 获取路由所需的代币账户
    fn get_route_token_accounts(
        &self,
        source_mint: &Pubkey,
        destination_mint: &Pubkey,
        owner: &Pubkey,
    ) -> Result<(Pubkey, Pubkey)> {
        // 获取关联代币账户地址
        let source_ata = get_associated_token_address(owner, source_mint);
        let destination_ata = get_associated_token_address(owner, destination_mint);
        Ok((source_ata, destination_ata))
    }

    /// 路由执行前的处理
    fn before_route_execution(
        &self,
        owner: &AccountInfo<'info>,
        source_token_account: &AccountInfo<'info>,
        amount_in: u64,
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<u64> {
        // 验证权限
        self.validate_route_authority(owner, source_token_account, owner_seeds)?;

        // 检查余额
        let token_data = TokenAccount::try_deserialize(
            &mut source_token_account.data.borrow().as_ref()
        )?;

        if token_data.amount < amount_in {
            return Err(RouteError::InsufficientBalance.into());
        }

        msg!("路由执行前检查通过 - 权限: {}, 余额: {}, 需要: {}",
            owner.key(), token_data.amount, amount_in);

        Ok(token_data.amount)
    }

    /// 路由执行后的处理
    fn after_route_execution(
        &self,
        destination_token_account: &AccountInfo<'info>,
        expected_amount_out: u64,
        _owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<u64> {
        // 查询最终余额
        let token_data = TokenAccount::try_deserialize(
            &mut destination_token_account.data.borrow().as_ref()
        )?;

        let actual_amount = token_data.amount;

        // 验证输出满足预期
        if actual_amount < expected_amount_out {
            msg!("警告: 实际输出 {} 低于预期 {}", actual_amount, expected_amount_out);
        }

        msg!("路由执行完成 - 最终余额: {}, 预期: {}",
            actual_amount, expected_amount_out);

        Ok(actual_amount)
    }

    /// 验证路由权限
    fn validate_route_authority(
        &self,
        owner: &AccountInfo<'info>,
        token_account: &AccountInfo<'info>,
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<()> {
        // 获取代币账户数据
        let token_data = TokenAccount::try_deserialize(
            &mut token_account.data.borrow().as_ref()
        )?;

        // 验证账户所有者
        if token_data.owner != *owner.key {
            return Err(RouteError::TokenAccountOwnerMismatch.into());
        }

        // 如果不使用PDA，则需要签名
        if owner_seeds.is_none() && !owner.is_signer {
            return Err(RouteError::SwapAuthorityIsNotSigner.into());
        }

        Ok(())
    }
}

/// 统一的路由前检查
///
/// 验证交换权限、代币账户等基本要素
pub fn route_before_check(
    swap_authority: &AccountInfo,
    source_token_account: &AccountInfo,
    _destination_token: &Pubkey,
    hop_index: usize,
    _owner_seeds: Option<&[&[&[u8]]]>,
) -> Result<()> {
    // 1. 验证代币账户数据完整性
    let token_data = TokenAccount::try_deserialize(
        &mut source_token_account.data.borrow().as_ref()
    ).map_err(|_| RouteError::InvalidDexAccounts)?;

    // 2. 验证账户所有者权限
    if token_data.owner != *swap_authority.key {
        msg!("账户所有者不匹配: 期望 {}, 实际 {}",
            swap_authority.key(), token_data.owner);
        return Err(RouteError::TokenAccountOwnerMismatch.into());
    }

    // 3. 第一步必须有签名者（除非使用PDA）
    if hop_index == 0 && owner_seeds.is_none() {
        if !swap_authority.is_signer {
            msg!("第一步交换需要权限账户签名: {}", swap_authority.key());
            return Err(RouteError::SwapAuthorityIsNotSigner.into());
        }
    }

    // 4. 验证代币账户不是冻结状态
    if token_data.state != anchor_spl::token::spl_token::state::AccountState::Initialized {
        return Err(RouteError::InvalidDexAccounts.into());
    }

    // 5. 验证代币余额非零（除非这是中间步骤）
    if hop_index == 0 && token_data.amount == 0 {
        return Err(RouteError::InsufficientBalance.into());
    }

    msg!("路由前检查通过 - 步骤: {}, 权限: {}, 余额: {}",
        hop_index, swap_authority.key(), token_data.amount);
    Ok(())
}

/// 统一的CPI调用处理
///
/// 封装DEX特定的CPI调用逻辑，提供标准化的错误处理和日志记录
pub fn invoke_route_process<'info>(
    dex_processor: &dyn DexProcessor,
    route: &Route,
    accounts: &[AccountInfo<'info>],
    amount_in: u64,
    hop_index: usize,
    owner_seeds: Option<&[&[&[u8]]]>,
) -> Result<u64> {
    msg!("开始执行 {} 交换 - 步骤: {}, 输入: {}",
        dex_processor.dex_name(), hop_index + 1, amount_in);

    // 1. 验证账户结构
    dex_processor.validate_accounts(route, accounts, hop_index)?;

    // 2. 交换前处理
    let (before_balance, account_indices) = dex_processor.before_swap(
        route, accounts, hop_index
    )?;

    // 3. 执行实际的CPI调用
    let expected_output = dex_processor.execute_swap_cpi(
        route,
        accounts,
        amount_in,
        route.min_amount_out,
        owner_seeds,
    )?;

    // 4. 交换后处理，计算实际输出
    let actual_output = dex_processor.after_swap(
        route,
        accounts,
        hop_index,
        owner_seeds,
        before_balance,
        &account_indices,
    )?;

    // 5. 验证输出满足最小要求
    if actual_output < route.min_amount_out {
        msg!("滑点过高: 实际输出 {} < 最小要求 {}", actual_output, route.min_amount_out);
        return Err(RouteError::SlippageTooHigh.into());
    }

    // 6. 计算和记录滑点信息
    let slippage_bps = calculate_slippage_bps(expected_output, actual_output)?;

    msg!("{} 交换完成 - 预期: {}, 实际: {}, 滑点: {}基点",
        dex_processor.dex_name(), expected_output, actual_output, slippage_bps);

    Ok(actual_output)
}

/// 计算滑点百分比（基点）
pub fn calculate_slippage_bps(expected: u64, actual: u64) -> Result<u16> {
    if expected == 0 {
        return Ok(0);
    }

    let diff = if actual >= expected {
        0 // 正滑点（更好的价格）
    } else {
        expected.checked_sub(actual)
            .ok_or(RouteError::MathUnderflow)?
    };

    let slippage_bps = diff
        .checked_mul(10000)
        .ok_or(RouteError::MathOverflow)?
        .checked_div(expected)
        .ok_or(RouteError::DivisionByZero)?;

    // 限制在合理范围内（最大100%）
    Ok((slippage_bps as u16).min(10000))
}

/// 计算价格影响
pub fn calculate_price_impact_bps(
    amount_in: u64,
    amount_out: u64,
    expected_rate: u64, // 期望汇率，scaled by 1e6
) -> Result<u16> {
    if amount_in == 0 || expected_rate == 0 {
        return Ok(0);
    }

    // 计算实际汇率
    let actual_rate = amount_out
        .checked_mul(1_000_000)
        .ok_or(RouteError::MathOverflow)?
        .checked_div(amount_in)
        .ok_or(RouteError::DivisionByZero)?;

    // 计算价格影响百分比
    let diff = if actual_rate >= expected_rate {
        actual_rate - expected_rate
    } else {
        expected_rate - actual_rate
    };

    let impact_bps = diff
        .checked_mul(10000)
        .ok_or(RouteError::MathOverflow)?
        .checked_div(expected_rate)
        .ok_or(RouteError::DivisionByZero)?;

    Ok((impact_bps as u16).min(10000))
}

/// 验证交换是否满足滑点要求
pub fn validate_slippage_tolerance(
    expected_amount: u64,
    actual_amount: u64,
    max_slippage_bps: u16,
) -> Result<()> {
    let slippage = calculate_slippage_bps(expected_amount, actual_amount)?;

    if slippage > max_slippage_bps {
        msg!("滑点超限: {}基点 > {}基点", slippage, max_slippage_bps);
        return Err(RouteError::SlippageTooHigh.into());
    }

    Ok(())
}

/// 获取关联代币账户地址
pub fn get_associated_token_address(owner: &Pubkey, mint: &Pubkey) -> Pubkey {
    anchor_spl::associated_token::get_associated_token_address(owner, mint)
}

/// 验证关联代币账户是否存在且有效
pub fn validate_associated_token_account(
    ata_account: &AccountInfo,
    owner: &Pubkey,
    mint: &Pubkey,
) -> Result<()> {
    // 验证账户地址
    let expected_ata = get_associated_token_address(owner, mint);
    if *ata_account.key != expected_ata {
        return Err(RouteError::InvalidDexAccounts.into());
    }

    // 验证账户已初始化
    if ata_account.data_is_empty() {
        return Err(RouteError::InvalidDexAccounts.into());
    }

    // 验证代币账户数据
    let token_data = TokenAccount::try_deserialize(
        &mut ata_account.data.borrow().as_ref()
    ).map_err(|_| RouteError::InvalidDexAccounts)?;

    if token_data.mint != *mint {
        return Err(RouteError::TokenAccountMintMismatch.into());
    }

    if token_data.owner != *owner {
        return Err(RouteError::TokenAccountOwnerMismatch.into());
    }

    Ok(())
}

/// 创建关联代币账户的指令（如果需要）
pub fn create_ata_if_needed<'info>(
    payer: &AccountInfo<'info>,
    ata_account: &AccountInfo<'info>,
    owner: &AccountInfo<'info>,
    mint: &AccountInfo<'info>,
    system_program: &AccountInfo<'info>,
    token_program: &AccountInfo<'info>,
    associated_token_program: &AccountInfo<'info>,
) -> Result<()> {
    // 检查ATA是否已存在
    if !ata_account.data_is_empty() {
        return Ok(()); // 已存在，无需创建
    }

    // 创建ATA指令
    let cpi_accounts = anchor_spl::associated_token::Create {
        payer: payer.clone(),
        associated_token: ata_account.clone(),
        authority: owner.clone(),
        mint: mint.clone(),
        system_program: system_program.clone(),
        token_program: token_program.clone(),
    };

    let cpi_ctx = CpiContext::new(
        associated_token_program.clone(),
        cpi_accounts,
    );

    anchor_spl::associated_token::create(cpi_ctx)?;

    msg!("创建关联代币账户: {}", ata_account.key());
    Ok(())
}
