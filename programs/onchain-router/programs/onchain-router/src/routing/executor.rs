//! 通用路由执行器
//!
//! 提供统一的路由执行接口，管理所有DEX适配器调用

use anchor_lang::prelude::*;
use crate::error::RouteError;
use crate::adapters::{DexProcessor, invoke_route_process};
use crate::adapters::meteora::{MeteoraAmmProcessor, MeteoraLbProcessor};
use crate::adapters::orca::OrcaProcessor;
use crate::adapters::pumpswap::PumpSwapProcessor;
use crate::adapters::raydium::{RaydiumClmmProcessor, RaydiumCpmmProcessor};
use crate::routing::types::{Route, Dex, RouteResult};

/// 多跳路由执行器
/// 负责统一管理各种DEX适配器的调用和路由步骤的执行
pub struct MultiHopRouteExecutor;

impl MultiHopRouteExecutor {
    /// 执行单个路由步骤
    ///
    /// # 参数
    /// * `route` - 路由配置
    /// * `amount_in` - 输入数量
    /// * `accounts` - DEX特定的账户信息
    /// * `hop_index` - 当前步骤索引
    /// * `owner_seeds` - PDA签名种子（可选）
    ///
    /// # 返回
    /// 返回实际输出数量
    pub fn execute_hop<'info>(
        route: &Route,
        amount_in: u64,
        accounts: &[AccountInfo<'info>],
        hop_index: usize,
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<u64> {
        msg!("执行路由步骤 {} - DEX: {:?}, 输入: {}",
            hop_index + 1, route.dex, amount_in);

        // 验证输入参数
        if amount_in == 0 {
            return Err(RouteError::AmountValidationFailed.into());
        }

        if accounts.is_empty() {
            return Err(RouteError::InvalidDexAccounts.into());
        }

        // 根据DEX类型获取对应的适配器
        let dex_processor = Self::get_dex_processor(&route.dex)?;

        // 使用通用路由处理逻辑
        let output_amount = invoke_route_process(
            dex_processor.as_ref(),
            route,
            accounts,
            amount_in,
            hop_index,
            owner_seeds,
        )?;

        msg!("路由步骤 {} 完成 - 输出: {}", hop_index + 1, output_amount);
        Ok(output_amount)
    }

    /// 执行路由序列
    ///
    /// # 参数
    /// * `routes` - 路由步骤列表
    /// * `initial_amount` - 初始输入数量
    /// * `remaining_accounts` - 所有相关账户
    /// * `owner_seeds` - PDA签名种子（可选）
    ///
    /// # 返回
    /// 返回完整的路由执行结果
    pub fn execute_route_sequence<'info>(
        routes: &[Route],
        initial_amount: u64,
        remaining_accounts: &'info [AccountInfo<'info>],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<RouteResult> {
        msg!("开始执行路由序列 - 步骤数: {}, 初始数量: {}",
            routes.len(), initial_amount);

        // 验证路由序列
        Self::validate_route_sequence(routes)?;

        let mut current_amount = initial_amount;
        let mut account_offset = 0;
        let mut total_gas_used = 0u64;
        let start_time = Clock::get()?.unix_timestamp;

        // 执行每个路由步骤
        for (i, route) in routes.iter().enumerate() {
            // 获取当前步骤需要的账户
            let accounts_needed = Self::get_required_accounts_count(&route.dex);

            if account_offset + accounts_needed > remaining_accounts.len() {
                return Err(RouteError::InvalidDexAccounts.into());
            }

            let step_accounts = &remaining_accounts[account_offset..account_offset + accounts_needed];

            // 执行单步路由
            current_amount = Self::execute_hop(
                route,
                current_amount,
                step_accounts,
                i,
                owner_seeds,
            )?;

            // 累积Gas消耗估算
            let step_gas = Self::estimate_step_gas(&route.dex);
            total_gas_used = total_gas_used.saturating_add(step_gas);

            account_offset += accounts_needed;
        }

        let end_time = Clock::get()?.unix_timestamp;
        let execution_time = (end_time - start_time) as u64;

        // 计算实际滑点
        let actual_slippage_bps = Self::calculate_total_slippage(
            initial_amount,
            current_amount,
            routes,
        )?;

        let result = RouteResult {
            amount_in: 0,
            amount_out: current_amount,
            expected_amount: None,
            steps_executed: routes.len() as u8,
            gas_used: total_gas_used,
            actual_slippage_bps,
            net_profit: 0, // 线性路由不计算利润
        };

        msg!("路由序列执行完成 - 最终输出: {}, Gas消耗: {}, 耗时: {}s",
            current_amount, total_gas_used, execution_time);

        Ok(result)
    }

    /// 获取DEX适配器
    fn get_dex_processor(dex: &Dex) -> Result<Box<dyn DexProcessor>> {
        match dex {
            Dex::RaydiumClmm => {
                Ok(Box::new(RaydiumClmmProcessor::default()))
            }
            Dex::RaydiumCpmm => {
                Ok(Box::new(RaydiumCpmmProcessor::default()))
            }
            Dex::MeteoraAmm => {
                Ok(Box::new(MeteoraAmmProcessor::default()))
            }
            Dex::MeteoraLb => {
                Ok(Box::new(MeteoraLbProcessor::default()))
            }
            Dex::Orca => {
                Ok(Box::new(OrcaProcessor::default()))
            }
            Dex::PumpSwap => {
                Ok(Box::new(PumpSwapProcessor::default()))
            }
        }
    }

    /// 获取DEX所需的账户数量
    pub fn get_required_accounts_count(dex: &Dex) -> usize {
        match dex {
            Dex::RaydiumClmm => 12,   // CLMM需要更多账户（池子、tick数组等）
            Dex::RaydiumCpmm => 10,   // CPMM相对简单
            Dex::MeteoraAmm => 9,     // AMM标准账户
            Dex::MeteoraLb => 14,     // LB需要最多账户（bin数组等）
            Dex::Orca => 11, // Whirlpool中等复杂度
            Dex::PumpSwap => 8,       // PumpSwap最简单
        }
    }

    /// 估算单步Gas消耗
    pub fn estimate_step_gas(dex: &Dex) -> u64 {
        match dex {
            Dex::RaydiumClmm => 120_000,   // CLMM复杂度最高
            Dex::RaydiumCpmm => 80_000,    // CPMM中等
            Dex::MeteoraAmm => 70_000,     // AMM相对简单
            Dex::MeteoraLb => 100_000,     // LB复杂度较高
            Dex::Orca => 90_000,  // Whirlpool中等偏高
            Dex::PumpSwap => 60_000,       // PumpSwap最简单
        }
    }

    /// 验证路由序列的有效性
    fn validate_route_sequence(routes: &[Route]) -> Result<()> {
        if routes.is_empty() {
            return Err(RouteError::EmptyRoutePath.into());
        }

        if routes.len() > 6 {
            return Err(RouteError::RoutePathTooLong.into());
        }

        // 验证每个路由步骤
        for (i, route) in routes.iter().enumerate() {
            route.validate()?;

            // 验证路由连续性（相邻步骤的代币要匹配）
            if i > 0 {
                let prev_output = routes[i - 1].output_mint;
                let curr_input = route.input_mint;

                if prev_output != curr_input {
                    msg!("路由不连续: 步骤{} 输出 {} != 步骤{} 输入 {}",
                        i, prev_output, i + 1, curr_input);
                    return Err(RouteError::RouteDiscontinuity.into());
                }
            }
        }

        Ok(())
    }

    /// 计算总体滑点
    fn calculate_total_slippage(
        initial_amount: u64,
        final_amount: u64,
        routes: &[Route],
    ) -> Result<u16> {
        // 计算期望的最小输出（基于所有步骤的min_amount_out）
        let mut expected_min = initial_amount;
        for route in routes {
            if route.min_amount_out > 0 {
                // 简化计算：假设每步的最小输出比例相等
                expected_min = expected_min
                    .checked_mul(route.min_amount_out)
                    .ok_or(RouteError::MathOverflow)?
                    .checked_div(initial_amount.max(1))
                    .ok_or(RouteError::DivisionByZero)?;
            }
        }

        // 计算实际滑点
        if expected_min == 0 {
            return Ok(0);
        }

        let slippage_bps = if final_amount >= expected_min {
            0 // 无滑点或正滑点
        } else {
            let diff = expected_min.saturating_sub(final_amount);
            diff.checked_mul(10000)
                .ok_or(RouteError::MathOverflow)?
                .checked_div(expected_min)
                .ok_or(RouteError::DivisionByZero)? as u16
        };

        Ok(slippage_bps.min(10000)) // 限制在100%以内
    }

    /// 预估路由序列的总输出
    pub fn estimate_total_output(
        routes: &[Route],
        initial_amount: u64,
    ) -> Result<u64> {
        let mut current_amount = initial_amount;

        for route in routes {
            // 使用DEX特定的估算逻辑
            current_amount = Self::estimate_route_output(route, current_amount)?;
        }

        Ok(current_amount)
    }

    /// 估算单步路由输出
    fn estimate_route_output(route: &Route, amount_in: u64) -> Result<u64> {
        // 根据DEX类型使用不同的费率和计算方法
        let (fee_rate_bps, price_impact_bps) = match route.dex {
            Dex::RaydiumClmm => (25, 5),    // 0.25%费用 + 0.05%价格影响
            Dex::RaydiumCpmm => (30, 10),   // 0.30%费用 + 0.10%价格影响
            Dex::MeteoraAmm => (20, 8),     // 0.20%费用 + 0.08%价格影响
            Dex::MeteoraLb => (15, 5),      // 0.15%费用 + 0.05%价格影响（动态）
            Dex::Orca => (30, 12), // 0.30%费用 + 0.12%价格影响
            Dex::PumpSwap => (50, 20),      // 0.50%费用 + 0.20%价格影响
        };

        let total_cost_bps = fee_rate_bps + price_impact_bps;
        let amount_out = amount_in
            .checked_mul(10000 - total_cost_bps)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::DivisionByZero)?;

        Ok(amount_out)
    }

    /// 验证路由序列的流动性充足性
    pub fn validate_route_liquidity(
        routes: &[Route],
        initial_amount: u64,
    ) -> Result<()> {
        let mut current_amount = initial_amount;

        for (i, route) in routes.iter().enumerate() {
            // 检查当前步骤的最小输出要求
            if route.min_amount_out == 0 {
                msg!("警告: 步骤 {} 未设置最小输出保护", i + 1);
            }

            // 估算输出并检查是否满足下一步的需求
            current_amount = Self::estimate_route_output(route, current_amount)?;

            if current_amount < route.min_amount_out {
                msg!("流动性不足: 步骤 {} 预估输出 {} < 最小要求 {}",
                    i + 1, current_amount, route.min_amount_out);
                return Err(RouteError::InsufficientLiquidity.into());
            }
        }

        Ok(())
    }

    /// 获取路由序列的复杂度评分
    pub fn get_complexity_score(routes: &[Route]) -> u8 {
        let base_score = routes.len() as u8;
        let dex_diversity = Self::count_unique_dexes(routes);
        let complexity_bonus = Self::calculate_dex_complexity_bonus(routes);

        base_score + dex_diversity + complexity_bonus
    }

    /// 计算唯一DEX数量
    fn count_unique_dexes(routes: &[Route]) -> u8 {
        let mut dexes = std::collections::HashSet::new();
        for route in routes {
            dexes.insert(&route.dex);
        }
        dexes.len() as u8
    }

    /// 计算DEX复杂度奖励
    fn calculate_dex_complexity_bonus(routes: &[Route]) -> u8 {
        let mut bonus = 0u8;
        for route in routes {
            let dex_bonus = match route.dex {
                Dex::RaydiumClmm | Dex::MeteoraLb => 3, // 最复杂
                Dex::Orca => 2,                // 中等复杂
                Dex::RaydiumCpmm | Dex::MeteoraAmm => 1, // 标准复杂度
                Dex::PumpSwap => 0,                     // 最简单
            };
            bonus = bonus.saturating_add(dex_bonus);
        }
        bonus
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::routing::types::Dex;

    #[test]
    fn test_get_required_accounts_count() {
        assert_eq!(MultiHopRouteExecutor::get_required_accounts_count(&Dex::RaydiumClmm), 12);
        assert_eq!(MultiHopRouteExecutor::get_required_accounts_count(&Dex::PumpSwap), 8);
    }

    #[test]
    fn test_estimate_step_gas() {
        assert_eq!(MultiHopRouteExecutor::estimate_step_gas(&Dex::RaydiumClmm), 120_000);
        assert_eq!(MultiHopRouteExecutor::estimate_step_gas(&Dex::PumpSwap), 60_000);
    }

    #[test]
    fn test_calculate_total_slippage() {
        let result = MultiHopRouteExecutor::calculate_total_slippage(
            100_000,
            99_000,
            &[]
        ).unwrap();
        assert!(result <= 10000); // 应该在合理范围内
    }

    #[test]
    fn test_estimate_route_output() {
        let route = Route {
            dex: Dex::RaydiumCpmm,
            input_mint: Pubkey::default(),
            output_mint: Pubkey::default(),
            swap_data: vec![1, 2, 3],
            min_amount_out: 0,
        };

        let output = MultiHopRouteExecutor::estimate_route_output(&route, 100_000).unwrap();
        assert!(output < 100_000); // 应该扣除费用
        assert!(output > 95_000);  // 但不应该扣除太多
    }
}
