//! 批量路由实现
//!
//! 处理 [A1, A2] -> [B1, B2] 类型的批量路由
//! 同时处理多个独立的路由操作

use anchor_lang::prelude::*;
use crate::error::RouteError;
use super::types::*;
use super::{linear::LinearRouteExecutor, circular::CircularRouteExecutor};

/// 批量路由执行器
pub struct BatchedRouteExecutor;

impl BatchedRouteExecutor {
    /// 执行批量路由
    ///
    /// # 参数
    /// * `config` - 批量路由配置
    /// * `remaining_accounts` - 所有需要的账户信息
    /// * `owner_seeds` - PDA签名种子（可选）
    ///
    /// # 返回
    /// 返回每个路由的输出数量列表
    pub fn execute<'info>(
        config: &BatchRouteConfig,
        remaining_accounts: &'info [AccountInfo<'info>],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<Vec<u64>> {
        msg!("开始执行批量路由 - 路由数量: {}, 原子性: {}",
            config.routes.len(), config.atomic);

        // 1. 验证批量路由配置
        Self::validate_config(config)?;

        // 2. 检查账户冲突
        let account_assignments = Self::assign_accounts_to_routes(config, remaining_accounts)?;

        if config.atomic {
            // 3a. 原子性执行：全部成功或全部失败
            Self::execute_atomic(config, &account_assignments, owner_seeds)
        } else {
            // 3b. 非原子性执行：允许部分失败
            Self::execute_non_atomic(config, &account_assignments, owner_seeds)
        }
    }

    /// 验证批量路由配置
    pub fn validate_config(config: &BatchRouteConfig) -> Result<()> {
        if config.routes.is_empty() {
            return Err(RouteError::EmptyRoutePath.into());
        }

        // 检查路由数量限制（避免过多的并发操作）
        if config.routes.len() > 10 {
            return Err(RouteError::RoutePathTooLong.into());
        }

        // 验证每个路由配置
        for route_config in &config.routes {
            route_config.validate()?;
        }

        Ok(())
    }

    /// 分配账户给各个路由
    fn assign_accounts_to_routes<'info>(
        config: &BatchRouteConfig,
        remaining_accounts: &'info [AccountInfo<'info>],
    ) -> Result<Vec<&'info [AccountInfo<'info>]>> {
        let mut assignments = Vec::new();
        let mut account_offset = 0;

        for (i, route_config) in config.routes.iter().enumerate() {
            let accounts_needed = Self::calculate_route_accounts_needed(route_config);

            if account_offset + accounts_needed > remaining_accounts.len() {
                msg!("路由 {} 账户不足: 需要 {}, 剩余 {}",
                    i, accounts_needed, remaining_accounts.len() - account_offset);
                return Err(RouteError::InvalidDexAccounts.into());
            }

            let route_accounts = &remaining_accounts[account_offset..account_offset + accounts_needed];
            assignments.push(route_accounts);
            account_offset += accounts_needed;

            msg!("路由 {} 分配账户: {} 个", i, accounts_needed);
        }

        Ok(assignments)
    }

    /// 原子性执行批量路由
    fn execute_atomic<'info>(
        config: &BatchRouteConfig,
        account_assignments: &[&'info [AccountInfo<'info>]],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<Vec<u64>> {
        msg!("原子性执行批量路由");

        let mut results = Vec::new();

        // 先执行所有路由，如果任意一个失败则回滚
        for (i, (route_config, accounts)) in config.routes.iter()
            .zip(account_assignments.iter()).enumerate() {

            msg!("执行原子批量路由 {} - 模式: {:?}", i + 1, route_config.mode);

            let result = match route_config.mode {
                RoutingMode::Linear => {
                    LinearRouteExecutor::execute(
                        &route_config.routes,
                        route_config.amount_in,
                        accounts,
                        owner_seeds,
                    )?
                }
                RoutingMode::Circular => {
                    CircularRouteExecutor::execute(
                        &route_config.routes,
                        route_config.amount_in,
                        accounts,
                        route_config.flash_loan.as_ref(),
                        owner_seeds,
                    )?
                }
                _ => {
                    msg!("不支持的批量路由模式: {:?}", route_config.mode);
                    return Err(RouteError::InvalidRoutingMode.into());
                }
            };

            results.push(result);
            msg!("原子批量路由 {} 成功 - 输出: {}", i + 1, result);
        }

        msg!("所有原子批量路由执行成功");
        Ok(results)
    }

    /// 非原子性执行批量路由
    fn execute_non_atomic<'info>(
        config: &BatchRouteConfig,
        account_assignments: &[&'info [AccountInfo<'info>]],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<Vec<u64>> {
        msg!("非原子性执行批量路由");

        let mut results = Vec::new();
        let mut success_count = 0;

        for (i, (route_config, accounts)) in config.routes.iter()
            .zip(account_assignments.iter()).enumerate() {

            msg!("执行非原子批量路由 {} - 模式: {:?}", i + 1, route_config.mode);

            let result = match route_config.mode {
                RoutingMode::Linear => {
                    LinearRouteExecutor::execute(
                        &route_config.routes,
                        route_config.amount_in,
                        accounts,
                        owner_seeds,
                    )
                }
                RoutingMode::Circular => {
                    CircularRouteExecutor::execute(
                        &route_config.routes,
                        route_config.amount_in,
                        accounts,
                        route_config.flash_loan.as_ref(),
                        owner_seeds,
                    )
                }
                _ => {
                    msg!("不支持的批量路由模式: {:?}", route_config.mode);
                    Err(RouteError::InvalidRoutingMode.into())
                }
            };

            match result {
                Ok(output) => {
                    results.push(output);
                    success_count += 1;
                    msg!("非原子批量路由 {} 成功 - 输出: {}", i + 1, output);
                }
                Err(e) => {
                    results.push(0); // 失败的路由返回0
                    msg!("非原子批量路由 {} 失败: {:?}", i + 1, e);
                }
            }
        }

        msg!("非原子批量路由执行完成 - 成功: {}/{}",
            success_count, config.routes.len());

        // 如果所有路由都失败，返回错误
        if success_count == 0 {
            return Err(RouteError::OperationTimeout.into());
        }

        Ok(results)
    }

    /// 计算单个路由所需的账户数量
    fn calculate_route_accounts_needed(route_config: &RouteConfig) -> usize {
        route_config.routes.iter().map(|route| {
            match route.dex {
                Dex::RaydiumClmm => 12,
                Dex::RaydiumCpmm => 10,
                Dex::MeteoraAmm => 9,
                Dex::MeteoraLb => 14,
                Dex::Orca => 11,
                Dex::PumpSwap => 8,
            }
        }).sum()
    }

    /// 执行详细的批量路由（包含完整结果）
    pub fn execute_detailed<'info>(
        config: &BatchRouteConfig,
        remaining_accounts: &'info [AccountInfo<'info>],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<BatchRouteResult> {
        msg!("开始执行详细批量路由");

        // 验证配置
        Self::validate_config(config)?;

        // 分配账户
        let account_assignments = Self::assign_accounts_to_routes(config, remaining_accounts)?;

        // 执行路由
        let route_outputs = if config.atomic {
            Self::execute_atomic(config, &account_assignments, owner_seeds)?
        } else {
            Self::execute_non_atomic(config, &account_assignments, owner_seeds)?
        };

        // 计算统计信息
        let total_output: u64 = route_outputs.iter().sum();
        let success_count = route_outputs.iter().filter(|&&x| x > 0).count() as u8;
        let total_gas = Self::estimate_gas_cost(config);

        let result = BatchRouteResult {
            route_outputs,
            total_output,
            success_count,
            total_routes: config.routes.len() as u8,
            atomic_execution: config.atomic,
            total_gas_used: total_gas,
        };

        msg!("详细批量路由执行完成: {:?}", result);
        Ok(result)
    }

    /// 估算批量路由的Gas消耗
    pub fn estimate_gas_cost(config: &BatchRouteConfig) -> u64 {
        let base_cost = 35_000; // 批量处理的基础开销
        let route_costs: u64 = config.routes.iter()
            .map(|route_config| {
                match route_config.mode {
                    RoutingMode::Linear => {
                        LinearRouteExecutor::estimate_gas_cost(&route_config.routes)
                    }
                    RoutingMode::Circular => {
                        CircularRouteExecutor::estimate_gas_cost(&route_config.routes)
                    }
                    _ => 50_000 // 其他模式的估算
                }
            })
            .sum();

        let batch_overhead = config.routes.len() as u64 * 5_000; // 每个路由的额外开销

        base_cost + route_costs + batch_overhead
    }

    /// 检查是否可以并行执行（无账户冲突）
    pub fn can_execute_parallel(config: &BatchRouteConfig) -> bool {
        // 检查各个路由是否使用了相同的代币账户
        let mut used_mints = std::collections::HashSet::new();

        for route_config in &config.routes {
            // 检查输入代币
            if let Some(first_route) = route_config.routes.first() {
                if used_mints.contains(&first_route.input_mint) {
                    return false; // 发现冲突
                }
                used_mints.insert(first_route.input_mint);
            }

            // 检查中间代币
            for route in &route_config.routes {
                if used_mints.contains(&route.output_mint) {
                    return false; // 发现冲突
                }
                used_mints.insert(route.output_mint);
            }
        }

        // 检查路由数量限制（太多的并行路由可能导致Gas不足）
        if config.routes.len() > 5 {
            return false;
        }

        true
    }

    /// 验证批量路由的可行性
    pub fn validate_feasibility(config: &BatchRouteConfig) -> Result<()> {
        // 验证基础配置
        Self::validate_config(config)?;

        // 验证各个路由的可行性
        for (i, route_config) in config.routes.iter().enumerate() {
            route_config.validate().map_err(|_| {
                msg!("批量路由 {} 配置验证失败", i);
                RouteError::InvalidRouteConfig
            })?;
        }

        // 验证Gas消耗
        let estimated_gas = Self::estimate_gas_cost(config);
        if estimated_gas > 1_300_000 { // 保留一些余量
            msg!("批量路由Gas消耗过高: {}", estimated_gas);
            return Err(RouteError::OperationTimeout.into());
        }

        msg!("批量路由可行性验证通过");
        Ok(())
    }
}
