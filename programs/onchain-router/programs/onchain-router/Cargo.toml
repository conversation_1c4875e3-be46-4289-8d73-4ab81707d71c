[package]
name = "onchain-router"
version.workspace = true
description = "通用多跳路由智能合约 - 支持线性、循环、分支等路由模式"
edition.workspace = true

[lib]
crate-type = ["cdylib", "lib"]
name = "onchain_router"

[features]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
cpi = ["no-entrypoint"]
default = []

[dependencies]
# Anchor framework
anchor-lang.workspace = true
anchor-spl.workspace = true

num-bigint.workspace = true


