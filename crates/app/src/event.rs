use std::fmt;
use solana_sdk::pubkey::Pubkey;
use tokio::sync::mpsc;
use arbitrage_engine::{ArbitragePath};
use data_parser::{ParsedAccount};
use shared::TokenMintInfo;

/// 池更新事件
#[derive(Debug, Clone)]
pub struct PoolUpdateEvent {
    /// 池地址
    pub pool_address: Pubkey,
    /// 更新的池价格数据
    pub parsed_account: ParsedAccount,
    /// 事件时间戳
    pub timestamp: u64,
}

impl PoolUpdateEvent {
    pub fn new(pool_address: Pubkey, parsed_account: ParsedAccount) -> Self {
        Self {
            pool_address,
            parsed_account,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        }
    }
}

#[derive(Debug, Clone)]
pub struct PoolTokenMintUpdateEvent {
    /// 池地址
    pub token_mint_info: TokenMintInfo,
    pub pool_address: String,
    /// 事件时间戳
    pub timestamp: u64,
}

impl PoolTokenMintUpdateEvent {
    pub fn new(token_mint_info: TokenMintInfo, pool_address: String) -> Self {
        Self {
            token_mint_info,
            pool_address,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        }
    }
}


/// 套利机会事件
#[derive(Debug, Clone)]
pub struct ArbitrageEvent {
    /// 发现的套利机会
    pub opportunities: Vec<ArbitragePath>,
    /// 事件时间戳
    pub timestamp: u64,
}

impl ArbitrageEvent {
    pub fn new(opportunities: Vec<ArbitragePath>) -> Self {
        Self {
            opportunities,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        }
    }
}

/// 系统事件枚举
pub enum SystemEvent {
    /// 池更新事件
    PoolUpdate(PoolUpdateEvent),
    PoolTokenMintUpdate(PoolTokenMintUpdateEvent),
    /// 套利机会事件
    ArbitrageOpportunity(ArbitrageEvent),
}

impl fmt::Display for SystemEvent {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            SystemEvent::PoolUpdate(event) => {
                write!(f, "PoolUpdate({})", event.pool_address)
            }
            SystemEvent::PoolTokenMintUpdate(event) => {
                write!(f, "PoolTokenMintUpdate({}, balance: {})", event.token_mint_info.token_vault, event.token_mint_info.token_balance)
            }
            SystemEvent::ArbitrageOpportunity(event) => {
                write!(f, "ArbitrageOpportunity({} opportunities)", event.opportunities.len())
            }
        }
    }
}

/// 事件发布器类型
pub type EventSender = mpsc::UnboundedSender<SystemEvent>;

/// 事件接收器类型
pub type EventReceiver = mpsc::UnboundedReceiver<SystemEvent>;

/// 创建事件通道
pub fn create_event_channel() -> (EventSender, EventReceiver) {
    mpsc::unbounded_channel()
}

/// 事件处理结果
pub type EventResult<T> = Result<T, EventError>;

/// 事件处理错误
#[derive(Debug, thiserror::Error)]
pub enum EventError {
    #[error("Event channel closed")]
    ChannelClosed,

    #[error("Failed to send event: {0}")]
    SendFailed(String),

    #[error("Event processing failed: {0}")]
    ProcessingFailed(String),
}

/// 事件发布助手宏
#[macro_export]
macro_rules! publish_event {
    ($sender:expr, $event:expr) => {
        if let Err(e) = $sender.send($event) {
            tracing::warn!("Failed to publish event: {}", e);
        }
    };
}

/// 异步事件处理器trait
#[async_trait::async_trait]
pub trait EventHandler: Send + Sync {
    /// 处理系统事件
    async fn handle_event(&self, event: SystemEvent) -> EventResult<()>;

    /// 获取处理器名称
    fn name(&self) -> &'static str;
}

/// 简单的事件总线
pub struct EventBus {
    sender: EventSender,
}

impl EventBus {
    /// 创建新的事件总线
    pub fn new(sender: EventSender) -> Self {
        Self { sender }
    }

    /// 发布池更新事件
    pub fn publish_pool_update(&self, pool_address: Pubkey, data_parser: ParsedAccount) -> EventResult<()> {
        let event = SystemEvent::PoolUpdate(PoolUpdateEvent::new(pool_address, data_parser));
        self.sender.send(event).map_err(|e| EventError::SendFailed(e.to_string()))
    }

    // 发布套利机会事件
    // pub fn publish_arbitrage_opportunities(&self, opportunities: Vec<ArbitrageOpportunity>) -> EventResult<()> {
    //     if !opportunities.is_empty() {
    //         let event = SystemEvent::ArbitrageOpportunity(ArbitrageEvent::new(opportunities));
    //         self.sender.send(event).map_err(|e| EventError::SendFailed(e.to_string()))
    //     } else {
    //         Ok(())
    //     }
    // }
}
